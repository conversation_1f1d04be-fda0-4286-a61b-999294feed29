{"name": "istio build-tools", "image": "gcr.io/istio-testing/build-tools:master-8a2789ab3019c53605c7fdbfaee770ad7b0eb01c", "privileged": true, "remoteEnv": {"USE_GKE_GCLOUD_AUTH_PLUGIN": "True", "BUILD_WITH_CONTAINER": "0", "CARGO_HOME": "/home/<USER>", "RUSTUP_HOME": "/home/<USER>"}, "features": {"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {}, "ghcr.io/mpriscella/features/kind:1": {}}, "customizations": {"vscode": {"extensions": ["golang.go", "rust-lang.rust-analyzer", "eamodio.gitlens", "zxh404.vscode-proto3", "ms-azuretools.vscode-docker", "redhat.vscode-yaml", "IBM.output-colorizer"], "settings": {"files.eol": "\n", "go.useLanguageServer": true, "go.lintTool": "golangci-lint"}}}}