# Configures Depdendabot to PR go security updates only

version: 2
updates:
  # Go configuration for master branch
  - package-ecosystem: "gomod"
    directory: "/"
    schedule:
      interval: "daily"
    # Limit number of open PRs to 0 so that we only get security updates
    # See https://docs.github.com/en/code-security/dependabot/dependabot-security-updates/configuring-dependabot-security-updates
    open-pull-requests-limit: 0
    labels:
      - "release-notes-none"
