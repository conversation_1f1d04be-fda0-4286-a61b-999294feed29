<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="841px" height="921px" viewBox="-0.5 -0.5 841 921" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2022-02-07T14:27:23.678Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/16.4.0 Chrome/96.0.4664.110 Electron/16.0.7 Safari/537.36&quot; etag=&quot;RiG4u8Tp3rCxqIMyjSx_&quot; version=&quot;16.4.0&quot; type=&quot;device&quot; pages=&quot;12&quot;&gt;&lt;diagram name=&quot;SPIRE&quot; id=&quot;wLrx4QkrcoeoC5oZ94Lo&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;Istio&quot; id=&quot;Rzn6Sl9NuGLtMh5HtDPn&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;Mithril&quot; id=&quot;uroO4yZ9FKbQ3iLUDdjh&quot;&gt;7Vxbd5s4EP41fkyOuRo/xnHSbTfZzdbds82+7MEgg1qMXFBiu79+JZC4CgdsMNStc04MgxhgZvTNRWNGyu169y4wN+4jsoE3ksf2bqTMR7IsKdqYfFHKPqYYY0ZwAmizQSlhAb8DRuTDXqANwtxAjJCH4SZPtJDvAwvnaGYQoG1+2Ap5+atuTAeUCAvL9MrUf6CNXfYU/LEo/TcAHZdfWeLPtzb5YEYIXdNG2wxJuRsptwFCON5a726BR4XH5bL/qP7tGOS49JfxIC+388dH/ypmdt/klOQRAuDjdlnLMetX03th8mLPivdcgAF68W1AmYxHymzrQgwWG9OiR7fEZAjNxWuP7Elks+adsid6BQEGu4ye2J2/A2gNcLAnQ9hRhelgnzeubUajXG9uRpu6zogmsyIn4ZxKimwwYTUQnNKy4FbQ826Rh4LoXGWl0T9CD3GAvoLMET360DOQjzP0+NOlCuS8DpSyDmSRDuTOdKD+dDpQCypQyzqYGAIdJLprXQdaxzpYrWTLEunA1pe6pndp7wVhS5OysCVdIOzEg7QubP1iha0XLVuvKWzZ6ErYk8sV9uBkbQhkrXvksjMbvuZkrn97ofFWhLxXYRRt3pABkrbZpQfJlsO+PT74eC6Lp/cf78jRG4fqgbEkjxlzzV+JkKM75tQWDcbWgGGrIoMx5KWSuKMFu9q4K+tRiuYjwkVZFIx1ZT3TkpyBTXIAtosC7CIH+aZ3l1JneU2kYx4Q2jD5fwEY71lCY75glNdOiM0A39AEhRB85ANOu4f07iO2kT5iBpJ6lEaJ7oL9Z8ruWuO7z+wOop35jl0r3tuzPdsM3ejppIQ9z37kxsgSopfAAofG8cTODBxwiKHMVEX1c9DWAuCZGL7mcziR4bBTnxCMpjiPkwqxqqQUbC++U3ZWwfyS2zjeInl+kgG0e4Atl5De20TmEEOSE4us9sFckgQ8Z2mmBx2fbFvkREAMZEbnKiQZ7g07sIa2HRs1IFhmLtn8V2Yb+nzRE2uzkTYXWGRNQ5hWgESStrOLjrKZsQg8rsbXksxixmN1zIeg1SoE3ahPKqmvHVfyPsQQ2ZU+pE1vYQJjJQwvdMsAy9XxtnCyw5CmfTsMSe3DY6RYnkXyDLBXYHkdTwN2EH/m1yHbGc5kL2VMdzjf5t4o51WK5tO9l9FrehmjbSdzmrGVU+Qf2hfwydOOM5hO9Bw4yKf5Bn4/cv6MDl2FKCdv6CrUSleRZh3jG852GQiSjj79SFd+wxhaniEZg000jk8sEs9Uzxn16wK4us/vAsQ4o2tGzkZVqWB78ROV8oxywlIw9hKjrhMWHuEfX4GhKBZhUgHIRM6uVrFkwC6Q16vacIG0Op/TPV+UO9E2k8Cjeycol9PdY6zn9PpdLU86a+BJu6/q9edf9cEV8nj013owtZgv6gZLZ00EmxX1eJKXJnbP2bxOmOS17YCVmg6Y281AkjBZtF4/GIS681/R/hcyVSJTEh31h0yiXoMLRKYjEOZYNGsbmYyayMQz9qEgk9anopvUC1upX1qeGYbQKuSwovVxwwLi9fGloalanxbEnclQLKhcglp/elj8QPkUnwMtrS8Z+ZIiN4DhrjfJp/c/nCE6aVKBvOTopFiq6T84Ob1486t9hqPXudtntL6tRykXb/5ANigJnzwfHhUq05V9sRWdtPV9jUi5efWXmmvO3Is7KVRPBK24oh66ztRY7ll4oq0GzbWY9B9WdSx2qcUzaUvQ8nhebYkqXT2vLVWH5i0H2krtItKwAm2luoh0YhEABK+QiCxTAepyTbf11Or4yo7We81ZKVd2eMRyi9Yb5AO24tYQRdOooiIOuQAUFTVyKWeF0V6LJ/kqGT9yljo8t9o3IZQPHAqElmsVfHnupNmWNjVWpIkXMNsSXOxvutX4TczlRi21y4PD6j9UqotLbUUtdEw/7Wk9hjLG0CIZVdS1Xpydvl1oJcs1dNUKaPJdXsqBEKeQkuv6TL6/b6ySN+dRRuKaQOCcdmqv17gAx+NpnkXdXq+J9gajjnu9VFHu2UqjTdzhRVWy8UyfIgMOzNWKoHODJc6Olyhq2p5ahQeNlyTG15LEf1DSdofXuMBWzrPobsVCrfH+gzLWHIKXBK/fhJcE8y8bXrTpdaGB72iAKbPqGmK6apd4BKH7Q8MKnzc/PazMP7r+hw/u3N5v9W9ooW938Pmquo+0HFYeovAKDg1RiWbbjDjrriP1kB9O2CR/KwKdthCB/g7/kP79c7p5Vh7DL981yVPxN6H6CqI3w038lqcV3FENzDYggOTqdP7NySXgJgRPKengdE1eyJT7YcK47BlUeTKZzQjdoyAwM62vTmQC+cUN8ikoj/6CISSGAX3nU5SbUm8E19Ebp/j3HK4dIiwPLsl/uFyT/0sPWV8t14Q+2bEophCsMjH4j6SyLgpIhHQdvjodGoZUmMGiX1yqAsNQmxsG2U3fgBXDQPoeMeXufw==&lt;/diagram&gt;&lt;diagram id=&quot;nusi7aoPp_d3XSAY3VtE&quot; name=&quot;istio-citadel-old&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;bGRr-YyPZtWwIx4oG2uz&quot; name=&quot;istio-spire-old&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;Istio Proposal Architecture&quot; id=&quot;yZ4f70IxWIf9K4ZqY44Z&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;Istio Envoy SDS Proposal Architecture&quot; id=&quot;YdhgSrL9EDRLY5uCNdXA&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;fJNOQbAEN-djurgDd3CZ&quot; name=&quot;Istio Proposal Flow&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;dY4jbbQjLFsdM4TPPS3_&quot; name=&quot;Istio Proposal Flow-Overview&quot;&gt;1Vldc9o6EP01fqTjb8wjn2nmNg0tzeT2vnSEJRs1xvLIAkx/fSUsGxu5IeFioMMD3rW0Hp2ze7wLmjVcZncUJIsHAlGkmTrMNGukmaZhOTr/Ep5t7ul2rdwRUgzlor1jhn8h6ZT7whWGKK0tZIREDCd1p0/iGPms5gOUkk19WUCi+lMTECLFMfNBpHqfMWSL3OsVxxL+jwiHi+LJhi7vLEGxWDrSBYBkU3FZY80aUkJYfrXMhigS4BW4dJNHHY5g8uTpL/6T/vCjM5l38mCT92wpj0BRzE4O/fJ5+vO/L/+stovHZ/bTeRjNn0adgqU1iFYSMHlYti0Q3CwwQ7ME+MLe8CzRrMGCLSNuGfwSpEnOW4AzxJ81CHAUDUlEKPfFJOa7Bimj5KXE3yw9xTLNtHTddYdDfueNh5WgrBFlKKtQLQ9/h8gSMbrlS+Rds6BcJnJhbvZZYXelb1HJiNIJZCaGZeg92vxCAt4M/t2X7UP0o//RePxKJ9/GQ39NRh0VexSvyVYhgJJVDAW0O7yP0FFFn8MKAfICvwlw1/fQPGgkpy0KbNOpUdBTKTDMBgrcthgwFAbuU4YJd/VDcfY/M6G/m4mxKz4Xxdsy6ylfinIFcKsJ8FIEz464qSA+G80UnHkg/n5AxzFuDTn7ADlXRa7XJBZt4WYpuGUCNz2hJFMV4+r4OYeZd238bDXvpveTyZj7ngl9iQiAouqn92dV3yAITL9RfaE7d51Lq0FdfS3v2vLrKKRgIb/wrLoLHeRBu4kDz5xb7mU5sHv6cQ6cthS5sQVUuxCIU5/wM/0FutKEX1u60txAK+ipoMWwLwYZbvkRSFOcCwKgTHVXIEQZZv+KjP/gSOu7zH9xPcqqxlYa74M9JSvqo+MvawRrI5ZKTgX8ptwtfBRFgOF1fTBrYkQ+YUqw6MFK/erV9cs8LIn8PHJXdRI6CGTb9UBlwRWBODchYkqgXYKUxz49Zzz1XdTUA3HUP4E5H8fr41aEw1jkDKcYcSEbiOLBfN7tyxtLDKGIMaAoxb/AfBdPJEcizrM7oTPQnFFjurya44dVWg7t8iFadS5uql79g2VbvRr2RY6dmhvFEhIEKWqFLbVjvWaFc6boNt/UdQr7exFQGPt9O6slaZD9aF4pr6zTb0pCHOMPbel7JcTp1gNdWkJ6SlL2b1pCiiI6g4QYnmv9ZRKiDh9vk5CjWmFcoRs4WvL2TZW8rdcr1T25azj8/fDCJW+orWb4dToUJQTE7put/SL7/3ftd3jx33qpu28T5haqvdIZ1BoD/eqNwWtj542qhNM7l0ocTp4nqwQ39/815cv3/9hZ498=&lt;/diagram&gt;&lt;diagram id=&quot;MUFqZ3DN9yHDJQhBZFO7&quot; name=&quot;Federation overview&quot;&gt;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&lt;/diagram&gt;&lt;diagram name=&quot;Federation over SDS&quot; id=&quot;hYVGwzMwZU1U64vE26MH&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;AN5Yn8OVrMXDBt4KsLJe&quot; name=&quot;SDS Flow&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="60.66" width="715.22" height="748.13" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/><path d="M 538.23 487.65 L 594.87 487.65 C 619.57 487.65 639.59 508.95 639.59 535.23 C 639.59 561.5 619.57 582.81 594.87 582.81 L 538.23 582.81 C 513.53 582.81 493.51 561.5 493.51 535.23 C 493.51 508.95 513.53 487.65 538.23 487.65 Z" fill="#d5e8d4" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 144px; height: 1px; padding-top: 535px; margin-left: 495px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Envoy fetches secrets directly from Spire Agent</b></div></div></div></foreignObject><text x="567" y="539" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Envoy fetches secrets di...</text></switch></g><ellipse cx="357.61" cy="118.94" rx="60.869565217391305" ry="35.6819650937298" fill="#ffe6cc" stroke="#d79b00" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 120px; height: 1px; padding-top: 119px; margin-left: 298px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span id="docs-internal-guid-33015ad8-7fff-a860-7e46-59e9f2e36c16"><b><span style="font-size: 11pt ; font-family: &quot;arial&quot; ; background-color: transparent ; font-style: italic ; vertical-align: baseline">istio-agent </span><span style="font-size: 11pt ; font-family: &quot;arial&quot; ; background-color: transparent ; vertical-align: baseline">starts</span></b></span></div></div></div></foreignObject><text x="358" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">istio-agent starts</text></switch></g><path d="M 357.61 214.09 L 418.48 273.56 L 357.61 333.03 L 296.74 273.56 Z" fill="#fff2cc" stroke="#d6b656" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 120px; height: 1px; padding-top: 274px; margin-left: 298px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>SDS socket exists?</b> </div></div></div></foreignObject><text x="358" y="277" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">SDS socket exists? </text></switch></g><path d="M 357.61 154.62 L 357.6 184.4 L 357.6 205.86" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 357.6 211.86 L 353.6 203.86 L 357.6 205.86 L 361.6 203.86 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 566.55 428.18 L 566.55 479.42" fill="none" stroke="#7ea6e0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 566.55 485.42 L 562.55 477.42 L 566.55 479.42 L 570.55 477.42 Z" fill="#7ea6e0" stroke="#7ea6e0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="493.51" y="356.82" width="146.09" height="71.36" rx="10.7" ry="10.7" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 144px; height: 1px; padding-top: 393px; margin-left: 495px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Doesn't start SDS server</b></div></div></div></foreignObject><text x="567" y="396" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Doesn't start SDS server</text></switch></g><path d="M 418.48 273.56 L 566.6 273.6 L 566.56 348.58" fill="none" stroke="#7ea6e0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 566.55 354.58 L 562.56 346.58 L 566.56 348.58 L 570.56 346.59 Z" fill="#7ea6e0" stroke="#7ea6e0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 187.17 333.03 L 248.04 392.5 L 187.17 451.97 L 126.3 392.5 Z" fill="#fff2cc" stroke="#d6b656" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 120px; height: 1px; padding-top: 393px; margin-left: 127px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Certificates files exist?</b></div></div></div></foreignObject><text x="187" y="396" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Certificates files e...</text></switch></g><path d="M 306.22 262.86 L 168.2 262.9 L 168.18 342.99" fill="none" stroke="#b5739d" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 168.18 348.99 L 164.18 340.99 L 168.18 342.99 L 172.18 340.99 Z" fill="#b5739d" stroke="#b5739d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="16.74" y="499.55" width="146.09" height="71.36" rx="10.7" ry="10.7" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 144px; height: 1px; padding-top: 535px; margin-left: 18px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Starts SDS server with the configured caClient</b></div></div></div></foreignObject><text x="90" y="539" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Starts SDS server with t...</text></switch></g><path d="M 126.3 392.5 L 89.8 392.5 L 89.78 491.31" fill="none" stroke="#b5739d" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 89.78 497.31 L 85.78 489.31 L 89.78 491.31 L 93.78 489.31 Z" fill="#b5739d" stroke="#b5739d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><rect x="211.52" y="499.55" width="146.09" height="71.36" rx="10.7" ry="10.7" fill="#dae8fc" stroke="#6c8ebf" stroke-width="2" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 144px; height: 1px; padding-top: 535px; margin-left: 213px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Starts SDS server without caClient</b></div></div></div></foreignObject><text x="285" y="539" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Starts SDS server withou...</text></switch></g><path d="M 248.04 392.5 L 284.6 392.5 L 284.57 491.31" fill="none" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 4" pointer-events="none"/><path d="M 284.57 497.31 L 280.57 489.31 L 284.57 491.31 L 288.57 489.31 Z" fill="#67ab9f" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 158.85 687.47 L 215.5 687.47 C 240.2 687.47 260.22 708.77 260.22 735.05 C 260.22 761.32 240.2 782.62 215.5 782.62 L 158.85 782.62 C 134.15 782.62 114.13 761.32 114.13 735.05 C 114.13 708.77 134.15 687.47 158.85 687.47 Z" fill="#d5e8d4" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 144px; height: 1px; padding-top: 735px; margin-left: 115px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b>Envoy fetches secrets directly from istio-agent</b></div></div></div></foreignObject><text x="187" y="739" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Envoy fetches secrets di...</text></switch></g><path d="M 89.78 570.91 L 89.8 629.2 L 169.2 629.2 L 169.2 680.85" fill="none" stroke="#b5739d" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 169.21 686.85 L 165.2 678.85 L 169.2 680.85 L 173.2 678.85 Z" fill="#b5739d" stroke="#b5739d" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 284.57 570.91 L 284.6 629.2 L 208.9 629.2 L 208.94 678.47" fill="none" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 4" pointer-events="none"/><path d="M 208.94 684.47 L 204.93 676.48 L 208.94 678.47 L 212.93 676.47 Z" fill="#67ab9f" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 59px; height: 1px; padding-top: 245px; margin-left: 420px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="color: rgb(0 , 0 , 0) ; font-family: &quot;helvetica&quot; ; font-size: 12px ; font-style: normal ; letter-spacing: normal ; text-align: center ; text-indent: 0px ; text-transform: none ; word-spacing: 0px ; background-color: rgb(248 , 249 , 250)">Yes</b></div></div></div></foreignObject><text x="420" y="257" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Yes</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 47px; height: 1px; padding-top: 245px; margin-left: 274px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="color: rgb(0 , 0 , 0) ; font-family: &quot;helvetica&quot; ; font-size: 12px ; font-style: normal ; letter-spacing: normal ; text-align: center ; text-indent: 0px ; text-transform: none ; word-spacing: 0px ; background-color: rgb(248 , 249 , 250)">No</b></div></div></div></foreignObject><text x="274" y="257" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">No</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 47px; height: 1px; padding-top: 364px; margin-left: 104px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="color: rgb(0 , 0 , 0) ; font-family: &quot;helvetica&quot; ; font-size: 12px ; font-style: normal ; letter-spacing: normal ; text-align: center ; text-indent: 0px ; text-transform: none ; word-spacing: 0px ; background-color: rgb(248 , 249 , 250)">No</b></div></div></div></foreignObject><text x="104" y="376" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">No</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 59px; height: 1px; padding-top: 364px; margin-left: 250px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><b style="color: rgb(0 , 0 , 0) ; font-family: &quot;helvetica&quot; ; font-size: 12px ; font-style: normal ; letter-spacing: normal ; text-align: center ; text-indent: 0px ; text-transform: none ; word-spacing: 0px ; background-color: rgb(248 , 249 , 250)">Yes</b></div></div></div></foreignObject><text x="250" y="376" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Yes</text></switch></g><path d="M 549.81 677.36 L 474.78 677.36" fill="none" stroke="#b5739d" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 549.81 778.46 L 474.78 778.46" fill="none" stroke="#7ea6e0" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 549.81 727.91 L 474.78 727.91" fill="none" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 4" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 104px; height: 1px; padding-top: 664px; margin-left: 561px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px"><b><span id="docs-internal-guid-b746e560-7fff-bd18-f8c4-338ba73c991a"><span style="background-color: transparent ; vertical-align: baseline">No SDS socket,<br />neither certificates</span></span></b></font></div></div></div></foreignObject><text x="561" y="676" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">No SDS socket,...</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 113px; height: 1px; padding-top: 715px; margin-left: 561px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font style="font-size: 12px"><b><span id="docs-internal-guid-b746e560-7fff-bd18-f8c4-338ba73c991a"><span style="background-color: transparent ; vertical-align: baseline">No SDS socket,<br />existing certificates</span></span></b></font></div></div></div></foreignObject><text x="561" y="727" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">No SDS socket,...</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 129px; height: 1px; padding-top: 765px; margin-left: 561px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><font face="helvetica"><b>Existing SDS socket</b></font></div></div></div></foreignObject><text x="561" y="777" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Existing SDS socket</text></switch></g><path d="M 304.9 281.41 L 204.6 281.4 L 204.58 341.8" fill="none" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="2 4" pointer-events="none"/><path d="M 204.58 347.8 L 200.59 339.8 L 204.58 341.8 L 208.59 339.81 Z" fill="#67ab9f" stroke="#67ab9f" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>