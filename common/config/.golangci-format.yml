# WARNING: DO NOT EDIT, THIS FILE IS PROBABLY A COPY
#
# The original version of this file is located in the https://github.com/istio/common-files repo.
# If you're looking at this file in a different repo and want to make a change, please go to the
# common-files repo, make the change there and check it in. Then come back to this repo and run
# "make update-common".

run:
  # Timeout for analysis, e.g. 30s, 5m.
  # Default: 1m
  timeout: 20m
  build-tags:
  - integ
  - integfuzz
linters:
  disable-all: true
  enable:
  - goimports
  - gofumpt
  - gci
  fast: false
linters-settings:
  gci:
    sections:
      - standard # Captures all standard packages if they do not match another section.
      - default # Contains all imports that could not be matched to another section type.
      - prefix(istio.io/) # Groups all imports with the specified Prefix.
  goimports:
    # put imports beginning with prefix after 3rd-party packages;
    # it's a comma-separated list of prefixes
    local-prefixes: istio.io/
issues:
  # Which dirs to exclude: issues from them won't be reported.
  # Can use regexp here: `generated.*`, regexp is applied on full path,
  # including the path prefix if one is set.
  # Default dirs are skipped independently of this option's value (see exclude-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work on Windows.
  # Default: []
  exclude-dirs:
    - genfiles$
    - vendor$
  # Which files to exclude: they will be analyzed, but issues from them won't be reported.
  # There is no need to include all autogenerated files,
  # we confidently recognize autogenerated files.
  # If it's not, please let us know.
  # "/" will be replaced by current OS file path separator to properly work on Windows.
  # Default: []
  exclude-files:
    - ".*\\.pb\\.go"
    - ".*\\.gen\\.go"
  # Maximum issues count per one linter.
  # Set to 0 to disable.
  # Default: 50
  max-issues-per-linter: 0
  # Maximum count of issues with the same text. Set to 0 to disable. Default is 3.
  max-same-issues: 0
