{"cniVersion": "0.4.0", "name": "dbnet", "plugins": [{"args": {"labels": {"appVersion": "1.0"}}, "bridge": "cni0", "dns": {"nameservers": ["********"]}, "ipam": {"gateway": "********", "subnet": "********/16", "type": "host-local"}, "type": "bridge"}, {"sysctl": {"net.core.somaxconn": "500"}, "type": "tuning"}, {"ambient_enabled": false, "cni_agent_run_dir": "/path/to/kubeconfig", "dns": {}, "exclude_namespaces": [""], "ipam": {}, "name": "istio-cni", "plugin_log_level": "debug", "type": "istio-cni"}]}