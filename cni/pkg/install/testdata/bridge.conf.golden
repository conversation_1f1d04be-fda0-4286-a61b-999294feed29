{"cniVersion": "0.3.1", "name": "k8s-pod-network", "plugins": [{"bridge": "cni0", "dns": {"nameservers": ["********"]}, "ipam": {"gateway": "********", "subnet": "********/16", "type": "host-local"}, "name": "dbnet", "type": "bridge"}, {"ambient_enabled": false, "cni_agent_run_dir": "/path/to/kubeconfig", "dns": {}, "exclude_namespaces": [""], "ipam": {}, "name": "istio-cni", "plugin_log_level": "debug", "type": "istio-cni"}]}