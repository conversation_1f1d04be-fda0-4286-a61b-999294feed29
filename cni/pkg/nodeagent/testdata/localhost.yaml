# This shows an example local config for ztunnel that adds a workload for localhost.
# This allows local testing by sending requests through the local ztunnel to other servers running on localhost.
workloads:
- uid: cluster1//v1/Pod/default/local
  name: local
  namespace: default
  serviceAccount: default
  workloadIps: ["127.0.0.1"]
  protocol: HBONE
  node: local
  network: ""
  services:
    "default/example.com":
      80: 8080
    "default/example2.com":
      80: 8080
# Define another local address, but this one uses TCP. This allows testing HBONE and TCP with one config.
- uid: cluster1//v1/Pod/default/local-tcp
  name: local-tcp
  namespace: default
  serviceAccount: default
  workloadIps: ["*********"]
  protocol: TCP
  node: local
  network: ""
  services:
    "default/example.com":
      80: 8080
    "default/example2.com":
      80: 8080
policies:
  - action: Allow
    rules:
    - - - not_destination_ports:
          - 9999
    name: deny-9999
    namespace: default
    scope: Namespace
services:
- name: local
  namespace: default
  hostname: example.com
  vips:
    - /**********
  ports:
    80: 8080
- name: remote
  namespace: default
  hostname: example2.com
  vips:
    - remote/**********
  ports:
    80: 8080