{"cniVersion": "0.3.1", "name": "k8s-pod-network", "plugins": [{"addIf": "true", "bridge": "mybridge", "ipMasq": true, "ipam": {"gateway": "********", "routes": [{"dst": "0.0.0.0/0"}], "subnet": "********/16", "type": "host-local"}, "isGateway": true, "mtu": 1460, "name": "rkt.kubernetes.io", "type": "bridge"}, {"ambient_enabled": false, "cni_agent_run_dir": "/tmp", "dns": {}, "exclude_namespaces": ["istio-system"], "ipam": {}, "name": "istio-cni", "plugin_log_level": "debug", "type": "istio-cni"}]}