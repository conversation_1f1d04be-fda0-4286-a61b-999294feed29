{"cniVersion": "0.3.1", "name": "k8s-pod-network", "plugins": [{"etcd_endpoints": "http://************:6666", "ipam": {"type": "calico-ipam"}, "kubernetes": {"kubeconfig": "/etc/cni/net.d/calico-kubeconfig"}, "mtu": 1500, "plugin_log_level": "info", "policy": {"type": "k8s"}, "type": "calico"}, {"capabilities": {"portMappings": true}, "snat": true, "type": "portmap"}, {"ambient_enabled": false, "cni_agent_run_dir": "/tmp", "dns": {}, "exclude_namespaces": ["istio-system"], "ipam": {}, "name": "istio-cni", "plugin_log_level": "debug", "type": "istio-cni"}]}