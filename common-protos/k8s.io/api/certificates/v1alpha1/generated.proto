/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.certificates.v1alpha1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/certificates/v1alpha1";

// ClusterTrustBundle is a cluster-scoped container for X.509 trust anchors
// (root certificates).
//
// ClusterTrustBundle objects are considered to be readable by any authenticated
// user in the cluster, because they can be mounted by pods using the
// `clusterTrustBundle` projection.  All service accounts have read access to
// ClusterTrustBundles by default.  Users who only have namespace-level access
// to a cluster can read ClusterTrustBundles by impersonating a serviceaccount
// that they have access to.
//
// It can be optionally associated with a particular assigner, in which case it
// contains one valid set of trust anchors for that signer. Signers may have
// multiple associated ClusterTrustBundles; each is an independent set of trust
// anchors for that signer. Admission control is used to enforce that only users
// with permissions on the signer can create or modify the corresponding bundle.
message ClusterTrustBundle {
  // metadata contains the object metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec contains the signer (if any) and trust anchors.
  optional ClusterTrustBundleSpec spec = 2;
}

// ClusterTrustBundleList is a collection of ClusterTrustBundle objects
message ClusterTrustBundleList {
  // metadata contains the list metadata.
  //
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is a collection of ClusterTrustBundle objects
  repeated ClusterTrustBundle items = 2;
}

// ClusterTrustBundleSpec contains the signer and trust anchors.
message ClusterTrustBundleSpec {
  // signerName indicates the associated signer, if any.
  //
  // In order to create or update a ClusterTrustBundle that sets signerName,
  // you must have the following cluster-scoped permission:
  // group=certificates.k8s.io resource=signers resourceName=<the signer name>
  // verb=attest.
  //
  // If signerName is not empty, then the ClusterTrustBundle object must be
  // named with the signer name as a prefix (translating slashes to colons).
  // For example, for the signer name `example.com/foo`, valid
  // ClusterTrustBundle object names include `example.com:foo:abc` and
  // `example.com:foo:v1`.
  //
  // If signerName is empty, then the ClusterTrustBundle object's name must
  // not have such a prefix.
  //
  // List/watch requests for ClusterTrustBundles can filter on this field
  // using a `spec.signerName=NAME` field selector.
  //
  // +optional
  optional string signerName = 1;

  // trustBundle contains the individual X.509 trust anchors for this
  // bundle, as PEM bundle of PEM-wrapped, DER-formatted X.509 certificates.
  //
  // The data must consist only of PEM certificate blocks that parse as valid
  // X.509 certificates.  Each certificate must include a basic constraints
  // extension with the CA bit set.  The API server will reject objects that
  // contain duplicate certificates, or that use PEM block headers.
  //
  // Users of ClusterTrustBundles, including Kubelet, are free to reorder and
  // deduplicate certificate blocks in this file according to their own logic,
  // as well as to drop PEM block headers and inter-block data.
  optional string trustBundle = 2;
}

