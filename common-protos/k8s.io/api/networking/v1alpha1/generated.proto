/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.networking.v1alpha1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/networking/v1alpha1";

// ClusterCIDR represents a single configuration for per-Node Pod CIDR
// allocations when the MultiCIDRRangeAllocator is enabled (see the config for
// kube-controller-manager).  A cluster may have any number of ClusterCIDR
// resources, all of which will be considered when allocating a CIDR for a
// Node.  A ClusterCIDR is eligible to be used for a given Node when the node
// selector matches the node in question and has free CIDRs to allocate.  In
// case of multiple matching ClusterCIDR resources, the allocator will attempt
// to break ties using internal heuristics, but any ClusterCIDR whose node
// selector matches the Node may be used.
message ClusterCIDR {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec is the desired state of the ClusterCIDR.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ClusterCIDRSpec spec = 2;
}

// ClusterCIDRList contains a list of ClusterCIDR.
message ClusterCIDRList {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of ClusterCIDRs.
  repeated ClusterCIDR items = 2;
}

// ClusterCIDRSpec defines the desired state of ClusterCIDR.
message ClusterCIDRSpec {
  // nodeSelector defines which nodes the config is applicable to.
  // An empty or nil nodeSelector selects all nodes.
  // This field is immutable.
  // +optional
  optional k8s.io.api.core.v1.NodeSelector nodeSelector = 1;

  // perNodeHostBits defines the number of host bits to be configured per node.
  // A subnet mask determines how much of the address is used for network bits
  // and host bits. For example an IPv4 address of ***********/24, splits the
  // address into 24 bits for the network portion and 8 bits for the host portion.
  // To allocate 256 IPs, set this field to 8 (a /24 mask for IPv4 or a /120 for IPv6).
  // Minimum value is 4 (16 IPs).
  // This field is immutable.
  // +required
  optional int32 perNodeHostBits = 2;

  // ipv4 defines an IPv4 IP block in CIDR notation(e.g. "10.0.0.0/8").
  // At least one of ipv4 and ipv6 must be specified.
  // This field is immutable.
  // +optional
  optional string ipv4 = 3;

  // ipv6 defines an IPv6 IP block in CIDR notation(e.g. "2001:db8::/64").
  // At least one of ipv4 and ipv6 must be specified.
  // This field is immutable.
  // +optional
  optional string ipv6 = 4;
}

// IPAddress represents a single IP of a single IP Family. The object is designed to be used by APIs
// that operate on IP addresses. The object is used by the Service core API for allocation of IP addresses.
// An IP address can be represented in different formats, to guarantee the uniqueness of the IP,
// the name of the object is the IP address in canonical format, four decimal digits separated
// by dots suppressing leading zeros for IPv4 and the representation defined by RFC 5952 for IPv6.
// Valid: *********** or 2001:db8::1 or 2001:db8:aaaa:bbbb:cccc:dddd:eeee:1
// Invalid: ********* or 2001:db8:0:0:0::1
message IPAddress {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec is the desired state of the IPAddress.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional IPAddressSpec spec = 2;
}

// IPAddressList contains a list of IPAddress.
message IPAddressList {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of IPAddresses.
  repeated IPAddress items = 2;
}

// IPAddressSpec describe the attributes in an IP Address.
message IPAddressSpec {
  // ParentRef references the resource that an IPAddress is attached to.
  // An IPAddress must reference a parent object.
  // +required
  optional ParentReference parentRef = 1;
}

// ParentReference describes a reference to a parent object.
message ParentReference {
  // Group is the group of the object being referenced.
  // +optional
  optional string group = 1;

  // Resource is the resource of the object being referenced.
  // +required
  optional string resource = 2;

  // Namespace is the namespace of the object being referenced.
  // +optional
  optional string namespace = 3;

  // Name is the name of the object being referenced.
  // +required
  optional string name = 4;

  // UID is the uid of the object being referenced.
  // +optional
  optional string uid = 5;
}

