/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.batch.v1beta1;

import "k8s.io/api/batch/v1/generated.proto";
import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/batch/v1beta1";

// CronJob represents the configuration of a single cron job.
message CronJob {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of a cron job, including the schedule.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional CronJobSpec spec = 2;

  // Current status of a cron job.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional CronJobStatus status = 3;
}

// CronJobList is a collection of cron jobs.
message CronJobList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of CronJobs.
  repeated CronJob items = 2;
}

// CronJobSpec describes how the job execution will look like and when it will actually run.
message CronJobSpec {
  // The schedule in Cron format, see https://en.wikipedia.org/wiki/Cron.
  optional string schedule = 1;

  // The time zone name for the given schedule, see https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.
  // If not specified, this will default to the time zone of the kube-controller-manager process.
  // The set of valid time zone names and the time zone offset is loaded from the system-wide time zone
  // database by the API server during CronJob validation and the controller manager during execution.
  // If no system-wide time zone database can be found a bundled version of the database is used instead.
  // If the time zone name becomes invalid during the lifetime of a CronJob or due to a change in host
  // configuration, the controller will stop creating new new Jobs and will create a system event with the
  // reason UnknownTimeZone.
  // More information can be found in https://kubernetes.io/docs/concepts/workloads/controllers/cron-jobs/#time-zones
  // +optional
  optional string timeZone = 8;

  // Optional deadline in seconds for starting the job if it misses scheduled
  // time for any reason.  Missed jobs executions will be counted as failed ones.
  // +optional
  optional int64 startingDeadlineSeconds = 2;

  // Specifies how to treat concurrent executions of a Job.
  // Valid values are:
  //
  // - "Allow" (default): allows CronJobs to run concurrently;
  // - "Forbid": forbids concurrent runs, skipping next run if previous run hasn't finished yet;
  // - "Replace": cancels currently running job and replaces it with a new one
  // +optional
  optional string concurrencyPolicy = 3;

  // This flag tells the controller to suspend subsequent executions, it does
  // not apply to already started executions.  Defaults to false.
  // +optional
  optional bool suspend = 4;

  // Specifies the job that will be created when executing a CronJob.
  optional JobTemplateSpec jobTemplate = 5;

  // The number of successful finished jobs to retain.
  // This is a pointer to distinguish between explicit zero and not specified.
  // Defaults to 3.
  // +optional
  optional int32 successfulJobsHistoryLimit = 6;

  // The number of failed finished jobs to retain.
  // This is a pointer to distinguish between explicit zero and not specified.
  // Defaults to 1.
  // +optional
  optional int32 failedJobsHistoryLimit = 7;
}

// CronJobStatus represents the current state of a cron job.
message CronJobStatus {
  // A list of pointers to currently running jobs.
  // +optional
  // +listType=atomic
  repeated k8s.io.api.core.v1.ObjectReference active = 1;

  // Information when was the last time the job was successfully scheduled.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastScheduleTime = 4;

  // Information when was the last time the job successfully completed.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastSuccessfulTime = 5;
}

// JobTemplateSpec describes the data a Job should have when created from a template
message JobTemplateSpec {
  // Standard object's metadata of the jobs created from this template.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of the job.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional k8s.io.api.batch.v1.JobSpec spec = 2;
}

