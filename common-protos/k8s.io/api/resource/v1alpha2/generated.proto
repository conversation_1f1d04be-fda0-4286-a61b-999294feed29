/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.resource.v1alpha2;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/resource/v1alpha2";

// AllocationResult contains attributes of an allocated resource.
message AllocationResult {
  // ResourceHandles contain the state associated with an allocation that
  // should be maintained throughout the lifetime of a claim. Each
  // ResourceHandle contains data that should be passed to a specific kubelet
  // plugin once it lands on a node. This data is returned by the driver
  // after a successful allocation and is opaque to Kubernetes. Driver
  // documentation may explain to users how to interpret this data if needed.
  //
  // Setting this field is optional. It has a maximum size of 32 entries.
  // If null (or empty), it is assumed this allocation will be processed by a
  // single kubelet plugin with no ResourceHandle data attached. The name of
  // the kubelet plugin invoked will match the DriverName set in the
  // ResourceClaimStatus this AllocationResult is embedded in.
  //
  // +listType=atomic
  // +optional
  repeated ResourceHandle resourceHandles = 1;

  // This field will get set by the resource driver after it has allocated
  // the resource to inform the scheduler where it can schedule Pods using
  // the ResourceClaim.
  //
  // Setting this field is optional. If null, the resource is available
  // everywhere.
  // +optional
  optional k8s.io.api.core.v1.NodeSelector availableOnNodes = 2;

  // Shareable determines whether the resource supports more
  // than one consumer at a time.
  // +optional
  optional bool shareable = 3;
}

// PodSchedulingContext objects hold information that is needed to schedule
// a Pod with ResourceClaims that use "WaitForFirstConsumer" allocation
// mode.
//
// This is an alpha type and requires enabling the DynamicResourceAllocation
// feature gate.
message PodSchedulingContext {
  // Standard object metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec describes where resources for the Pod are needed.
  optional PodSchedulingContextSpec spec = 2;

  // Status describes where resources for the Pod can be allocated.
  // +optional
  optional PodSchedulingContextStatus status = 3;
}

// PodSchedulingContextList is a collection of Pod scheduling objects.
message PodSchedulingContextList {
  // Standard list metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of PodSchedulingContext objects.
  repeated PodSchedulingContext items = 2;
}

// PodSchedulingContextSpec describes where resources for the Pod are needed.
message PodSchedulingContextSpec {
  // SelectedNode is the node for which allocation of ResourceClaims that
  // are referenced by the Pod and that use "WaitForFirstConsumer"
  // allocation is to be attempted.
  // +optional
  optional string selectedNode = 1;

  // PotentialNodes lists nodes where the Pod might be able to run.
  //
  // The size of this field is limited to 128. This is large enough for
  // many clusters. Larger clusters may need more attempts to find a node
  // that suits all pending resources. This may get increased in the
  // future, but not reduced.
  //
  // +listType=set
  // +optional
  repeated string potentialNodes = 2;
}

// PodSchedulingContextStatus describes where resources for the Pod can be allocated.
message PodSchedulingContextStatus {
  // ResourceClaims describes resource availability for each
  // pod.spec.resourceClaim entry where the corresponding ResourceClaim
  // uses "WaitForFirstConsumer" allocation mode.
  //
  // +listType=map
  // +listMapKey=name
  // +optional
  repeated ResourceClaimSchedulingStatus resourceClaims = 1;
}

// ResourceClaim describes which resources are needed by a resource consumer.
// Its status tracks whether the resource has been allocated and what the
// resulting attributes are.
//
// This is an alpha type and requires enabling the DynamicResourceAllocation
// feature gate.
message ResourceClaim {
  // Standard object metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec describes the desired attributes of a resource that then needs
  // to be allocated. It can only be set once when creating the
  // ResourceClaim.
  optional ResourceClaimSpec spec = 2;

  // Status describes whether the resource is available and with which
  // attributes.
  // +optional
  optional ResourceClaimStatus status = 3;
}

// ResourceClaimConsumerReference contains enough information to let you
// locate the consumer of a ResourceClaim. The user must be a resource in the same
// namespace as the ResourceClaim.
message ResourceClaimConsumerReference {
  // APIGroup is the group for the resource being referenced. It is
  // empty for the core API. This matches the group in the APIVersion
  // that is used when creating the resources.
  // +optional
  optional string apiGroup = 1;

  // Resource is the type of resource being referenced, for example "pods".
  optional string resource = 3;

  // Name is the name of resource being referenced.
  optional string name = 4;

  // UID identifies exactly one incarnation of the resource.
  optional string uid = 5;
}

// ResourceClaimList is a collection of claims.
message ResourceClaimList {
  // Standard list metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of resource claims.
  repeated ResourceClaim items = 2;
}

// ResourceClaimParametersReference contains enough information to let you
// locate the parameters for a ResourceClaim. The object must be in the same
// namespace as the ResourceClaim.
message ResourceClaimParametersReference {
  // APIGroup is the group for the resource being referenced. It is
  // empty for the core API. This matches the group in the APIVersion
  // that is used when creating the resources.
  // +optional
  optional string apiGroup = 1;

  // Kind is the type of resource being referenced. This is the same
  // value as in the parameter object's metadata, for example "ConfigMap".
  optional string kind = 2;

  // Name is the name of resource being referenced.
  optional string name = 3;
}

// ResourceClaimSchedulingStatus contains information about one particular
// ResourceClaim with "WaitForFirstConsumer" allocation mode.
message ResourceClaimSchedulingStatus {
  // Name matches the pod.spec.resourceClaims[*].Name field.
  // +optional
  optional string name = 1;

  // UnsuitableNodes lists nodes that the ResourceClaim cannot be
  // allocated for.
  //
  // The size of this field is limited to 128, the same as for
  // PodSchedulingSpec.PotentialNodes. This may get increased in the
  // future, but not reduced.
  //
  // +listType=set
  // +optional
  repeated string unsuitableNodes = 2;
}

// ResourceClaimSpec defines how a resource is to be allocated.
message ResourceClaimSpec {
  // ResourceClassName references the driver and additional parameters
  // via the name of a ResourceClass that was created as part of the
  // driver deployment.
  optional string resourceClassName = 1;

  // ParametersRef references a separate object with arbitrary parameters
  // that will be used by the driver when allocating a resource for the
  // claim.
  //
  // The object must be in the same namespace as the ResourceClaim.
  // +optional
  optional ResourceClaimParametersReference parametersRef = 2;

  // Allocation can start immediately or when a Pod wants to use the
  // resource. "WaitForFirstConsumer" is the default.
  // +optional
  optional string allocationMode = 3;
}

// ResourceClaimStatus tracks whether the resource has been allocated and what
// the resulting attributes are.
message ResourceClaimStatus {
  // DriverName is a copy of the driver name from the ResourceClass at
  // the time when allocation started.
  // +optional
  optional string driverName = 1;

  // Allocation is set by the resource driver once a resource or set of
  // resources has been allocated successfully. If this is not specified, the
  // resources have not been allocated yet.
  // +optional
  optional AllocationResult allocation = 2;

  // ReservedFor indicates which entities are currently allowed to use
  // the claim. A Pod which references a ResourceClaim which is not
  // reserved for that Pod will not be started.
  //
  // There can be at most 32 such reservations. This may get increased in
  // the future, but not reduced.
  //
  // +listType=map
  // +listMapKey=uid
  // +optional
  repeated ResourceClaimConsumerReference reservedFor = 3;

  // DeallocationRequested indicates that a ResourceClaim is to be
  // deallocated.
  //
  // The driver then must deallocate this claim and reset the field
  // together with clearing the Allocation field.
  //
  // While DeallocationRequested is set, no new consumers may be added to
  // ReservedFor.
  // +optional
  optional bool deallocationRequested = 4;
}

// ResourceClaimTemplate is used to produce ResourceClaim objects.
message ResourceClaimTemplate {
  // Standard object metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Describes the ResourceClaim that is to be generated.
  //
  // This field is immutable. A ResourceClaim will get created by the
  // control plane for a Pod when needed and then not get updated
  // anymore.
  optional ResourceClaimTemplateSpec spec = 2;
}

// ResourceClaimTemplateList is a collection of claim templates.
message ResourceClaimTemplateList {
  // Standard list metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of resource claim templates.
  repeated ResourceClaimTemplate items = 2;
}

// ResourceClaimTemplateSpec contains the metadata and fields for a ResourceClaim.
message ResourceClaimTemplateSpec {
  // ObjectMeta may contain labels and annotations that will be copied into the PVC
  // when creating it. No other fields are allowed and will be rejected during
  // validation.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec for the ResourceClaim. The entire content is copied unchanged
  // into the ResourceClaim that gets created from this template. The
  // same fields as in a ResourceClaim are also valid here.
  optional ResourceClaimSpec spec = 2;
}

// ResourceClass is used by administrators to influence how resources
// are allocated.
//
// This is an alpha type and requires enabling the DynamicResourceAllocation
// feature gate.
message ResourceClass {
  // Standard object metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // DriverName defines the name of the dynamic resource driver that is
  // used for allocation of a ResourceClaim that uses this class.
  //
  // Resource drivers have a unique name in forward domain order
  // (acme.example.com).
  optional string driverName = 2;

  // ParametersRef references an arbitrary separate object that may hold
  // parameters that will be used by the driver when allocating a
  // resource that uses this class. A dynamic resource driver can
  // distinguish between parameters stored here and and those stored in
  // ResourceClaimSpec.
  // +optional
  optional ResourceClassParametersReference parametersRef = 3;

  // Only nodes matching the selector will be considered by the scheduler
  // when trying to find a Node that fits a Pod when that Pod uses
  // a ResourceClaim that has not been allocated yet.
  //
  // Setting this field is optional. If null, all nodes are candidates.
  // +optional
  optional k8s.io.api.core.v1.NodeSelector suitableNodes = 4;
}

// ResourceClassList is a collection of classes.
message ResourceClassList {
  // Standard list metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of resource classes.
  repeated ResourceClass items = 2;
}

// ResourceClassParametersReference contains enough information to let you
// locate the parameters for a ResourceClass.
message ResourceClassParametersReference {
  // APIGroup is the group for the resource being referenced. It is
  // empty for the core API. This matches the group in the APIVersion
  // that is used when creating the resources.
  // +optional
  optional string apiGroup = 1;

  // Kind is the type of resource being referenced. This is the same
  // value as in the parameter object's metadata.
  optional string kind = 2;

  // Name is the name of resource being referenced.
  optional string name = 3;

  // Namespace that contains the referenced resource. Must be empty
  // for cluster-scoped resources and non-empty for namespaced
  // resources.
  // +optional
  optional string namespace = 4;
}

// ResourceHandle holds opaque resource data for processing by a specific kubelet plugin.
message ResourceHandle {
  // DriverName specifies the name of the resource driver whose kubelet
  // plugin should be invoked to process this ResourceHandle's data once it
  // lands on a node. This may differ from the DriverName set in
  // ResourceClaimStatus this ResourceHandle is embedded in.
  optional string driverName = 1;

  // Data contains the opaque data associated with this ResourceHandle. It is
  // set by the controller component of the resource driver whose name
  // matches the DriverName set in the ResourceClaimStatus this
  // ResourceHandle is embedded in. It is set at allocation time and is
  // intended for processing by the kubelet plugin whose name matches
  // the DriverName set in this ResourceHandle.
  //
  // The maximum size of this field is 16KiB. This may get increased in the
  // future, but not reduced.
  // +optional
  optional string data = 2;
}

