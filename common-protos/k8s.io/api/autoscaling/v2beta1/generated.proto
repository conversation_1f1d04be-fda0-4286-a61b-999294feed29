/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.autoscaling.v2beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/autoscaling/v2beta1";

// ContainerResourceMetricSource indicates how to scale on a resource metric known to
// Kubernetes, as specified in requests and limits, describing each pod in the
// current scale target (e.g. CPU or memory).  The values will be averaged
// together before being compared to the target.  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.  Only one "target" type
// should be set.
message ContainerResourceMetricSource {
  // name is the name of the resource in question.
  optional string name = 1;

  // targetAverageUtilization is the target value of the average of the
  // resource metric across all relevant pods, represented as a percentage of
  // the requested value of the resource for the pods.
  // +optional
  optional int32 targetAverageUtilization = 2;

  // targetAverageValue is the target value of the average of the
  // resource metric across all relevant pods, as a raw value (instead of as
  // a percentage of the request), similar to the "pods" metric source type.
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity targetAverageValue = 3;

  // container is the name of the container in the pods of the scaling target
  optional string container = 4;
}

// ContainerResourceMetricStatus indicates the current value of a resource metric known to
// Kubernetes, as specified in requests and limits, describing a single container in each pod in the
// current scale target (e.g. CPU or memory).  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.
message ContainerResourceMetricStatus {
  // name is the name of the resource in question.
  optional string name = 1;

  // currentAverageUtilization is the current value of the average of the
  // resource metric across all relevant pods, represented as a percentage of
  // the requested value of the resource for the pods.  It will only be
  // present if `targetAverageValue` was set in the corresponding metric
  // specification.
  // +optional
  optional int32 currentAverageUtilization = 2;

  // currentAverageValue is the current value of the average of the
  // resource metric across all relevant pods, as a raw value (instead of as
  // a percentage of the request), similar to the "pods" metric source type.
  // It will always be set, regardless of the corresponding metric specification.
  optional k8s.io.apimachinery.pkg.api.resource.Quantity currentAverageValue = 3;

  // container is the name of the container in the pods of the scaling target
  optional string container = 4;
}

// CrossVersionObjectReference contains enough information to let you identify the referred resource.
message CrossVersionObjectReference {
  // Kind of the referent; More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  optional string kind = 1;

  // Name of the referent; More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
  optional string name = 2;

  // API version of the referent
  // +optional
  optional string apiVersion = 3;
}

// ExternalMetricSource indicates how to scale on a metric not associated with
// any Kubernetes object (for example length of queue in cloud
// messaging service, or QPS from loadbalancer running outside of cluster).
// Exactly one "target" type should be set.
message ExternalMetricSource {
  // metricName is the name of the metric in question.
  optional string metricName = 1;

  // metricSelector is used to identify a specific time series
  // within a given metric.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector metricSelector = 2;

  // targetValue is the target value of the metric (as a quantity).
  // Mutually exclusive with TargetAverageValue.
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity targetValue = 3;

  // targetAverageValue is the target per-pod value of global metric (as a quantity).
  // Mutually exclusive with TargetValue.
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity targetAverageValue = 4;
}

// ExternalMetricStatus indicates the current value of a global metric
// not associated with any Kubernetes object.
message ExternalMetricStatus {
  // metricName is the name of a metric used for autoscaling in
  // metric system.
  optional string metricName = 1;

  // metricSelector is used to identify a specific time series
  // within a given metric.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector metricSelector = 2;

  // currentValue is the current value of the metric (as a quantity)
  optional k8s.io.apimachinery.pkg.api.resource.Quantity currentValue = 3;

  // currentAverageValue is the current value of metric averaged over autoscaled pods.
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity currentAverageValue = 4;
}

// HorizontalPodAutoscaler is the configuration for a horizontal pod
// autoscaler, which automatically manages the replica count of any resource
// implementing the scale subresource based on the metrics specified.
message HorizontalPodAutoscaler {
  // metadata is the standard object metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec is the specification for the behaviour of the autoscaler.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status.
  // +optional
  optional HorizontalPodAutoscalerSpec spec = 2;

  // status is the current information about the autoscaler.
  // +optional
  optional HorizontalPodAutoscalerStatus status = 3;
}

// HorizontalPodAutoscalerCondition describes the state of
// a HorizontalPodAutoscaler at a certain point.
message HorizontalPodAutoscalerCondition {
  // type describes the current condition
  optional string type = 1;

  // status is the status of the condition (True, False, Unknown)
  optional string status = 2;

  // lastTransitionTime is the last time the condition transitioned from
  // one status to another
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // reason is the reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // message is a human-readable explanation containing details about
  // the transition
  // +optional
  optional string message = 5;
}

// HorizontalPodAutoscaler is a list of horizontal pod autoscaler objects.
message HorizontalPodAutoscalerList {
  // metadata is the standard list metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of horizontal pod autoscaler objects.
  repeated HorizontalPodAutoscaler items = 2;
}

// HorizontalPodAutoscalerSpec describes the desired functionality of the HorizontalPodAutoscaler.
message HorizontalPodAutoscalerSpec {
  // scaleTargetRef points to the target resource to scale, and is used to the pods for which metrics
  // should be collected, as well as to actually change the replica count.
  optional CrossVersionObjectReference scaleTargetRef = 1;

  // minReplicas is the lower limit for the number of replicas to which the autoscaler
  // can scale down.  It defaults to 1 pod.  minReplicas is allowed to be 0 if the
  // alpha feature gate HPAScaleToZero is enabled and at least one Object or External
  // metric is configured.  Scaling is active as long as at least one metric value is
  // available.
  // +optional
  optional int32 minReplicas = 2;

  // maxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up.
  // It cannot be less that minReplicas.
  optional int32 maxReplicas = 3;

  // metrics contains the specifications for which to use to calculate the
  // desired replica count (the maximum replica count across all metrics will
  // be used).  The desired replica count is calculated multiplying the
  // ratio between the target value and the current value by the current
  // number of pods.  Ergo, metrics used must decrease as the pod count is
  // increased, and vice-versa.  See the individual metric source types for
  // more information about how each type of metric must respond.
  // +optional
  repeated MetricSpec metrics = 4;
}

// HorizontalPodAutoscalerStatus describes the current status of a horizontal pod autoscaler.
message HorizontalPodAutoscalerStatus {
  // observedGeneration is the most recent generation observed by this autoscaler.
  // +optional
  optional int64 observedGeneration = 1;

  // lastScaleTime is the last time the HorizontalPodAutoscaler scaled the number of pods,
  // used by the autoscaler to control how often the number of pods is changed.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastScaleTime = 2;

  // currentReplicas is current number of replicas of pods managed by this autoscaler,
  // as last seen by the autoscaler.
  optional int32 currentReplicas = 3;

  // desiredReplicas is the desired number of replicas of pods managed by this autoscaler,
  // as last calculated by the autoscaler.
  optional int32 desiredReplicas = 4;

  // currentMetrics is the last read state of the metrics used by this autoscaler.
  // +optional
  repeated MetricStatus currentMetrics = 5;

  // conditions is the set of conditions required for this autoscaler to scale its target,
  // and indicates whether or not those conditions are met.
  // +optional
  repeated HorizontalPodAutoscalerCondition conditions = 6;
}

// MetricSpec specifies how to scale based on a single metric
// (only `type` and one other matching field should be set at once).
message MetricSpec {
  // type is the type of metric source.  It should be one of "ContainerResource",
  // "External", "Object", "Pods" or "Resource", each mapping to a matching field in the object.
  // Note: "ContainerResource" type is available on when the feature-gate
  // HPAContainerMetrics is enabled
  optional string type = 1;

  // object refers to a metric describing a single kubernetes object
  // (for example, hits-per-second on an Ingress object).
  // +optional
  optional ObjectMetricSource object = 2;

  // pods refers to a metric describing each pod in the current scale target
  // (for example, transactions-processed-per-second).  The values will be
  // averaged together before being compared to the target value.
  // +optional
  optional PodsMetricSource pods = 3;

  // resource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing each pod in the
  // current scale target (e.g. CPU or memory). Such metrics are built in to
  // Kubernetes, and have special scaling options on top of those available
  // to normal per-pod metrics using the "pods" source.
  // +optional
  optional ResourceMetricSource resource = 4;

  // container resource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing a single container in
  // each pod of the current scale target (e.g. CPU or memory). Such metrics are
  // built in to Kubernetes, and have special scaling options on top of those
  // available to normal per-pod metrics using the "pods" source.
  // This is an alpha feature and can be enabled by the HPAContainerMetrics feature flag.
  // +optional
  optional ContainerResourceMetricSource containerResource = 7;

  // external refers to a global metric that is not associated
  // with any Kubernetes object. It allows autoscaling based on information
  // coming from components running outside of cluster
  // (for example length of queue in cloud messaging service, or
  // QPS from loadbalancer running outside of cluster).
  // +optional
  optional ExternalMetricSource external = 5;
}

// MetricStatus describes the last-read state of a single metric.
message MetricStatus {
  // type is the type of metric source.  It will be one of "ContainerResource",
  // "External", "Object", "Pods" or "Resource", each corresponds to a matching field in the object.
  // Note: "ContainerResource" type is available on when the feature-gate
  // HPAContainerMetrics is enabled
  optional string type = 1;

  // object refers to a metric describing a single kubernetes object
  // (for example, hits-per-second on an Ingress object).
  // +optional
  optional ObjectMetricStatus object = 2;

  // pods refers to a metric describing each pod in the current scale target
  // (for example, transactions-processed-per-second).  The values will be
  // averaged together before being compared to the target value.
  // +optional
  optional PodsMetricStatus pods = 3;

  // resource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing each pod in the
  // current scale target (e.g. CPU or memory). Such metrics are built in to
  // Kubernetes, and have special scaling options on top of those available
  // to normal per-pod metrics using the "pods" source.
  // +optional
  optional ResourceMetricStatus resource = 4;

  // container resource refers to a resource metric (such as those specified in
  // requests and limits) known to Kubernetes describing a single container in each pod in the
  // current scale target (e.g. CPU or memory). Such metrics are built in to
  // Kubernetes, and have special scaling options on top of those available
  // to normal per-pod metrics using the "pods" source.
  // +optional
  optional ContainerResourceMetricStatus containerResource = 7;

  // external refers to a global metric that is not associated
  // with any Kubernetes object. It allows autoscaling based on information
  // coming from components running outside of cluster
  // (for example length of queue in cloud messaging service, or
  // QPS from loadbalancer running outside of cluster).
  // +optional
  optional ExternalMetricStatus external = 5;
}

// ObjectMetricSource indicates how to scale on a metric describing a
// kubernetes object (for example, hits-per-second on an Ingress object).
message ObjectMetricSource {
  // target is the described Kubernetes object.
  optional CrossVersionObjectReference target = 1;

  // metricName is the name of the metric in question.
  optional string metricName = 2;

  // targetValue is the target value of the metric (as a quantity).
  optional k8s.io.apimachinery.pkg.api.resource.Quantity targetValue = 3;

  // selector is the string-encoded form of a standard kubernetes label selector for the given metric
  // When set, it is passed as an additional parameter to the metrics server for more specific metrics scoping
  // When unset, just the metricName will be used to gather metrics.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 4;

  // averageValue is the target value of the average of the
  // metric across all relevant pods (as a quantity)
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity averageValue = 5;
}

// ObjectMetricStatus indicates the current value of a metric describing a
// kubernetes object (for example, hits-per-second on an Ingress object).
message ObjectMetricStatus {
  // target is the described Kubernetes object.
  optional CrossVersionObjectReference target = 1;

  // metricName is the name of the metric in question.
  optional string metricName = 2;

  // currentValue is the current value of the metric (as a quantity).
  optional k8s.io.apimachinery.pkg.api.resource.Quantity currentValue = 3;

  // selector is the string-encoded form of a standard kubernetes label selector for the given metric
  // When set in the ObjectMetricSource, it is passed as an additional parameter to the metrics server for more specific metrics scoping.
  // When unset, just the metricName will be used to gather metrics.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 4;

  // averageValue is the current value of the average of the
  // metric across all relevant pods (as a quantity)
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity averageValue = 5;
}

// PodsMetricSource indicates how to scale on a metric describing each pod in
// the current scale target (for example, transactions-processed-per-second).
// The values will be averaged together before being compared to the target
// value.
message PodsMetricSource {
  // metricName is the name of the metric in question
  optional string metricName = 1;

  // targetAverageValue is the target value of the average of the
  // metric across all relevant pods (as a quantity)
  optional k8s.io.apimachinery.pkg.api.resource.Quantity targetAverageValue = 2;

  // selector is the string-encoded form of a standard kubernetes label selector for the given metric
  // When set, it is passed as an additional parameter to the metrics server for more specific metrics scoping
  // When unset, just the metricName will be used to gather metrics.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 3;
}

// PodsMetricStatus indicates the current value of a metric describing each pod in
// the current scale target (for example, transactions-processed-per-second).
message PodsMetricStatus {
  // metricName is the name of the metric in question
  optional string metricName = 1;

  // currentAverageValue is the current value of the average of the
  // metric across all relevant pods (as a quantity)
  optional k8s.io.apimachinery.pkg.api.resource.Quantity currentAverageValue = 2;

  // selector is the string-encoded form of a standard kubernetes label selector for the given metric
  // When set in the PodsMetricSource, it is passed as an additional parameter to the metrics server for more specific metrics scoping.
  // When unset, just the metricName will be used to gather metrics.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 3;
}

// ResourceMetricSource indicates how to scale on a resource metric known to
// Kubernetes, as specified in requests and limits, describing each pod in the
// current scale target (e.g. CPU or memory).  The values will be averaged
// together before being compared to the target.  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.  Only one "target" type
// should be set.
message ResourceMetricSource {
  // name is the name of the resource in question.
  optional string name = 1;

  // targetAverageUtilization is the target value of the average of the
  // resource metric across all relevant pods, represented as a percentage of
  // the requested value of the resource for the pods.
  // +optional
  optional int32 targetAverageUtilization = 2;

  // targetAverageValue is the target value of the average of the
  // resource metric across all relevant pods, as a raw value (instead of as
  // a percentage of the request), similar to the "pods" metric source type.
  // +optional
  optional k8s.io.apimachinery.pkg.api.resource.Quantity targetAverageValue = 3;
}

// ResourceMetricStatus indicates the current value of a resource metric known to
// Kubernetes, as specified in requests and limits, describing each pod in the
// current scale target (e.g. CPU or memory).  Such metrics are built in to
// Kubernetes, and have special scaling options on top of those available to
// normal per-pod metrics using the "pods" source.
message ResourceMetricStatus {
  // name is the name of the resource in question.
  optional string name = 1;

  // currentAverageUtilization is the current value of the average of the
  // resource metric across all relevant pods, represented as a percentage of
  // the requested value of the resource for the pods.  It will only be
  // present if `targetAverageValue` was set in the corresponding metric
  // specification.
  // +optional
  optional int32 currentAverageUtilization = 2;

  // currentAverageValue is the current value of the average of the
  // resource metric across all relevant pods, as a raw value (instead of as
  // a percentage of the request), similar to the "pods" metric source type.
  // It will always be set, regardless of the corresponding metric specification.
  optional k8s.io.apimachinery.pkg.api.resource.Quantity currentAverageValue = 3;
}

