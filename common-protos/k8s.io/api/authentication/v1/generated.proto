/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.authentication.v1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/authentication/v1";

// BoundObjectReference is a reference to an object that a token is bound to.
message BoundObjectReference {
  // Kind of the referent. Valid kinds are 'Pod' and 'Secret'.
  // +optional
  optional string kind = 1;

  // API version of the referent.
  // +optional
  optional string apiVersion = 2;

  // Name of the referent.
  // +optional
  optional string name = 3;

  // UID of the referent.
  // +optional
  optional string uID = 4;
}

// ExtraValue masks the value so protobuf can generate
// +protobuf.nullable=true
// +protobuf.options.(gogoproto.goproto_stringer)=false
message ExtraValue {
  // items, if empty, will result in an empty slice

  repeated string items = 1;
}

// TokenRequest requests a token for a given service account.
message TokenRequest {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec holds information about the request being evaluated
  optional TokenRequestSpec spec = 2;

  // Status is filled in by the server and indicates whether the token can be authenticated.
  // +optional
  optional TokenRequestStatus status = 3;
}

// TokenRequestSpec contains client provided parameters of a token request.
message TokenRequestSpec {
  // Audiences are the intendend audiences of the token. A recipient of a
  // token must identify themself with an identifier in the list of
  // audiences of the token, and otherwise should reject the token. A
  // token issued for multiple audiences may be used to authenticate
  // against any of the audiences listed but implies a high degree of
  // trust between the target audiences.
  repeated string audiences = 1;

  // ExpirationSeconds is the requested duration of validity of the request. The
  // token issuer may return a token with a different validity duration so a
  // client needs to check the 'expiration' field in a response.
  // +optional
  optional int64 expirationSeconds = 4;

  // BoundObjectRef is a reference to an object that the token will be bound to.
  // The token will only be valid for as long as the bound object exists.
  // NOTE: The API server's TokenReview endpoint will validate the
  // BoundObjectRef, but other audiences may not. Keep ExpirationSeconds
  // small if you want prompt revocation.
  // +optional
  optional BoundObjectReference boundObjectRef = 3;
}

// TokenRequestStatus is the result of a token request.
message TokenRequestStatus {
  // Token is the opaque bearer token.
  optional string token = 1;

  // ExpirationTimestamp is the time of expiration of the returned token.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time expirationTimestamp = 2;
}

// TokenReview attempts to authenticate a token to a known user.
// Note: TokenReview requests may be cached by the webhook token authenticator
// plugin in the kube-apiserver.
message TokenReview {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec holds information about the request being evaluated
  optional TokenReviewSpec spec = 2;

  // Status is filled in by the server and indicates whether the request can be authenticated.
  // +optional
  optional TokenReviewStatus status = 3;
}

// TokenReviewSpec is a description of the token authentication request.
message TokenReviewSpec {
  // Token is the opaque bearer token.
  // +optional
  optional string token = 1;

  // Audiences is a list of the identifiers that the resource server presented
  // with the token identifies as. Audience-aware token authenticators will
  // verify that the token was intended for at least one of the audiences in
  // this list. If no audiences are provided, the audience will default to the
  // audience of the Kubernetes apiserver.
  // +optional
  repeated string audiences = 2;
}

// TokenReviewStatus is the result of the token authentication request.
message TokenReviewStatus {
  // Authenticated indicates that the token was associated with a known user.
  // +optional
  optional bool authenticated = 1;

  // User is the UserInfo associated with the provided token.
  // +optional
  optional UserInfo user = 2;

  // Audiences are audience identifiers chosen by the authenticator that are
  // compatible with both the TokenReview and token. An identifier is any
  // identifier in the intersection of the TokenReviewSpec audiences and the
  // token's audiences. A client of the TokenReview API that sets the
  // spec.audiences field should validate that a compatible audience identifier
  // is returned in the status.audiences field to ensure that the TokenReview
  // server is audience aware. If a TokenReview returns an empty
  // status.audience field where status.authenticated is "true", the token is
  // valid against the audience of the Kubernetes API server.
  // +optional
  repeated string audiences = 4;

  // Error indicates that the token couldn't be checked
  // +optional
  optional string error = 3;
}

// UserInfo holds the information about the user needed to implement the
// user.Info interface.
message UserInfo {
  // The name that uniquely identifies this user among all active users.
  // +optional
  optional string username = 1;

  // A unique value that identifies this user across time. If this user is
  // deleted and another user by the same name is added, they will have
  // different UIDs.
  // +optional
  optional string uid = 2;

  // The names of groups this user is a part of.
  // +optional
  repeated string groups = 3;

  // Any additional information provided by the authenticator.
  // +optional
  map<string, ExtraValue> extra = 4;
}

