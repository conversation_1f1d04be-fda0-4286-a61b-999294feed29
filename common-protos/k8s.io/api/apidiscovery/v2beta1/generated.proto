/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.apidiscovery.v2beta1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/apidiscovery/v2beta1";

// APIGroupDiscovery holds information about which resources are being served for all version of the API Group.
// It contains a list of APIVersionDiscovery that holds a list of APIResourceDiscovery types served for a version.
// Versions are in descending order of preference, with the first version being the preferred entry.
message APIGroupDiscovery {
  // Standard object's metadata.
  // The only field completed will be name. For instance, resourceVersion will be empty.
  // name is the name of the API group whose discovery information is presented here.
  // name is allowed to be "" to represent the legacy, ungroupified resources.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // versions are the versions supported in this group. They are sorted in descending order of preference,
  // with the preferred version being the first entry.
  // +listType=map
  // +listMapKey=version
  repeated APIVersionDiscovery versions = 2;
}

// APIGroupDiscoveryList is a resource containing a list of APIGroupDiscovery.
// This is one of the types able to be returned from the /api and /apis endpoint and contains an aggregated
// list of API resources (built-ins, Custom Resource Definitions, resources from aggregated servers)
// that a cluster supports.
message APIGroupDiscoveryList {
  // ResourceVersion will not be set, because this does not have a replayable ordering among multiple apiservers.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of groups for discovery. The groups are listed in priority order.
  repeated APIGroupDiscovery items = 2;
}

// APIResourceDiscovery provides information about an API resource for discovery.
message APIResourceDiscovery {
  // resource is the plural name of the resource.  This is used in the URL path and is the unique identifier
  // for this resource across all versions in the API group.
  // Resources with non-empty groups are located at /apis/<APIGroupDiscovery.objectMeta.name>/<APIVersionDiscovery.version>/<APIResourceDiscovery.Resource>
  // Resources with empty groups are located at /api/v1/<APIResourceDiscovery.Resource>
  optional string resource = 1;

  // responseKind describes the group, version, and kind of the serialization schema for the object type this endpoint typically returns.
  // APIs may return other objects types at their discretion, such as error conditions, requests for alternate representations, or other operation specific behavior.
  // This value will be null if an APIService reports subresources but supports no operations on the parent resource
  optional k8s.io.apimachinery.pkg.apis.meta.v1.GroupVersionKind responseKind = 2;

  // scope indicates the scope of a resource, either Cluster or Namespaced
  optional string scope = 3;

  // singularResource is the singular name of the resource.  This allows clients to handle plural and singular opaquely.
  // For many clients the singular form of the resource will be more understandable to users reading messages and should be used when integrating the name of the resource into a sentence.
  // The command line tool kubectl, for example, allows use of the singular resource name in place of plurals.
  // The singular form of a resource should always be an optional element - when in doubt use the canonical resource name.
  optional string singularResource = 4;

  // verbs is a list of supported API operation types (this includes
  // but is not limited to get, list, watch, create, update, patch,
  // delete, deletecollection, and proxy).
  // +listType=set
  repeated string verbs = 5;

  // shortNames is a list of suggested short names of the resource.
  // +listType=set
  repeated string shortNames = 6;

  // categories is a list of the grouped resources this resource belongs to (e.g. 'all').
  // Clients may use this to simplify acting on multiple resource types at once.
  // +listType=set
  repeated string categories = 7;

  // subresources is a list of subresources provided by this resource. Subresources are located at /apis/<APIGroupDiscovery.objectMeta.name>/<APIVersionDiscovery.version>/<APIResourceDiscovery.Resource>/name-of-instance/<APIResourceDiscovery.subresources[i].subresource>
  // +listType=map
  // +listMapKey=subresource
  repeated APISubresourceDiscovery subresources = 8;
}

// APISubresourceDiscovery provides information about an API subresource for discovery.
message APISubresourceDiscovery {
  // subresource is the name of the subresource.  This is used in the URL path and is the unique identifier
  // for this resource across all versions.
  optional string subresource = 1;

  // responseKind describes the group, version, and kind of the serialization schema for the object type this endpoint typically returns.
  // Some subresources do not return normal resources, these will have null return types.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.GroupVersionKind responseKind = 2;

  // acceptedTypes describes the kinds that this endpoint accepts.
  // Subresources may accept the standard content types or define
  // custom negotiation schemes. The list may not be exhaustive for
  // all operations.
  // +listType=map
  // +listMapKey=group
  // +listMapKey=version
  // +listMapKey=kind
  repeated k8s.io.apimachinery.pkg.apis.meta.v1.GroupVersionKind acceptedTypes = 3;

  // verbs is a list of supported API operation types (this includes
  // but is not limited to get, list, watch, create, update, patch,
  // delete, deletecollection, and proxy). Subresources may define
  // custom verbs outside the standard Kubernetes verb set. Clients
  // should expect the behavior of standard verbs to align with
  // Kubernetes interaction conventions.
  // +listType=set
  repeated string verbs = 4;
}

// APIVersionDiscovery holds a list of APIResourceDiscovery types that are served for a particular version within an API Group.
message APIVersionDiscovery {
  // version is the name of the version within a group version.
  optional string version = 1;

  // resources is a list of APIResourceDiscovery objects for the corresponding group version.
  // +listType=map
  // +listMapKey=resource
  repeated APIResourceDiscovery resources = 2;

  // freshness marks whether a group version's discovery document is up to date.
  // "Current" indicates the discovery document was recently
  // refreshed. "Stale" indicates the discovery document could not
  // be retrieved and the returned discovery document may be
  // significantly out of date. Clients that require the latest
  // version of the discovery information be retrieved before
  // performing an operation should not use the aggregated document
  optional string freshness = 3;
}

