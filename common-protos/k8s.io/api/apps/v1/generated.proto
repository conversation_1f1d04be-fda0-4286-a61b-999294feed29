/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.apps.v1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";
import "k8s.io/apimachinery/pkg/util/intstr/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/apps/v1";

// ControllerRevision implements an immutable snapshot of state data. Clients
// are responsible for serializing and deserializing the objects that contain
// their internal state.
// Once a ControllerRevision has been successfully created, it can not be updated.
// The API Server will fail validation of all requests that attempt to mutate
// the Data field. ControllerRevisions may, however, be deleted. Note that, due to its use by both
// the DaemonSet and StatefulSet controllers for update and rollback, this object is beta. However,
// it may be subject to name and representation changes in future releases, and clients should not
// depend on its stability. It is primarily for internal use by controllers.
message ControllerRevision {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Data is the serialized representation of the state.
  optional k8s.io.apimachinery.pkg.runtime.RawExtension data = 2;

  // Revision indicates the revision of the state represented by Data.
  optional int64 revision = 3;
}

// ControllerRevisionList is a resource containing a list of ControllerRevision objects.
message ControllerRevisionList {
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of ControllerRevisions
  repeated ControllerRevision items = 2;
}

// DaemonSet represents the configuration of a daemon set.
message DaemonSet {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The desired behavior of this daemon set.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional DaemonSetSpec spec = 2;

  // The current status of this daemon set. This data may be
  // out of date by some window of time.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional DaemonSetStatus status = 3;
}

// DaemonSetCondition describes the state of a DaemonSet at a certain point.
message DaemonSetCondition {
  // Type of DaemonSet condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // The reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // A human readable message indicating details about the transition.
  // +optional
  optional string message = 5;
}

// DaemonSetList is a collection of daemon sets.
message DaemonSetList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // A list of daemon sets.
  repeated DaemonSet items = 2;
}

// DaemonSetSpec is the specification of a daemon set.
message DaemonSetSpec {
  // A label query over pods that are managed by the daemon set.
  // Must match in order to be controlled.
  // It must match the pod template's labels.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 1;

  // An object that describes the pod that will be created.
  // The DaemonSet will create exactly one copy of this pod on every node
  // that matches the template's node selector (or on every node if no node
  // selector is specified).
  // The only allowed template.spec.restartPolicy value is "Always".
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#pod-template
  optional k8s.io.api.core.v1.PodTemplateSpec template = 2;

  // An update strategy to replace existing DaemonSet pods with new pods.
  // +optional
  optional DaemonSetUpdateStrategy updateStrategy = 3;

  // The minimum number of seconds for which a newly created DaemonSet pod should
  // be ready without any of its container crashing, for it to be considered
  // available. Defaults to 0 (pod will be considered available as soon as it
  // is ready).
  // +optional
  optional int32 minReadySeconds = 4;

  // The number of old history to retain to allow rollback.
  // This is a pointer to distinguish between explicit zero and not specified.
  // Defaults to 10.
  // +optional
  optional int32 revisionHistoryLimit = 6;
}

// DaemonSetStatus represents the current status of a daemon set.
message DaemonSetStatus {
  // The number of nodes that are running at least 1
  // daemon pod and are supposed to run the daemon pod.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/daemonset/
  optional int32 currentNumberScheduled = 1;

  // The number of nodes that are running the daemon pod, but are
  // not supposed to run the daemon pod.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/daemonset/
  optional int32 numberMisscheduled = 2;

  // The total number of nodes that should be running the daemon
  // pod (including nodes correctly running the daemon pod).
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/daemonset/
  optional int32 desiredNumberScheduled = 3;

  // numberReady is the number of nodes that should be running the daemon pod and have one
  // or more of the daemon pod running with a Ready Condition.
  optional int32 numberReady = 4;

  // The most recent generation observed by the daemon set controller.
  // +optional
  optional int64 observedGeneration = 5;

  // The total number of nodes that are running updated daemon pod
  // +optional
  optional int32 updatedNumberScheduled = 6;

  // The number of nodes that should be running the
  // daemon pod and have one or more of the daemon pod running and
  // available (ready for at least spec.minReadySeconds)
  // +optional
  optional int32 numberAvailable = 7;

  // The number of nodes that should be running the
  // daemon pod and have none of the daemon pod running and available
  // (ready for at least spec.minReadySeconds)
  // +optional
  optional int32 numberUnavailable = 8;

  // Count of hash collisions for the DaemonSet. The DaemonSet controller
  // uses this field as a collision avoidance mechanism when it needs to
  // create the name for the newest ControllerRevision.
  // +optional
  optional int32 collisionCount = 9;

  // Represents the latest available observations of a DaemonSet's current state.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated DaemonSetCondition conditions = 10;
}

// DaemonSetUpdateStrategy is a struct used to control the update strategy for a DaemonSet.
message DaemonSetUpdateStrategy {
  // Type of daemon set update. Can be "RollingUpdate" or "OnDelete". Default is RollingUpdate.
  // +optional
  optional string type = 1;

  // Rolling update config params. Present only if type = "RollingUpdate".
  // ---
  // TODO: Update this to follow our convention for oneOf, whatever we decide it
  // to be. Same as Deployment `strategy.rollingUpdate`.
  // See https://github.com/kubernetes/kubernetes/issues/35345
  // +optional
  optional RollingUpdateDaemonSet rollingUpdate = 2;
}

// Deployment enables declarative updates for Pods and ReplicaSets.
message Deployment {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Specification of the desired behavior of the Deployment.
  // +optional
  optional DeploymentSpec spec = 2;

  // Most recently observed status of the Deployment.
  // +optional
  optional DeploymentStatus status = 3;
}

// DeploymentCondition describes the state of a deployment at a certain point.
message DeploymentCondition {
  // Type of deployment condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // The last time this condition was updated.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastUpdateTime = 6;

  // Last time the condition transitioned from one status to another.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 7;

  // The reason for the condition's last transition.
  optional string reason = 4;

  // A human readable message indicating details about the transition.
  optional string message = 5;
}

// DeploymentList is a list of Deployments.
message DeploymentList {
  // Standard list metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of Deployments.
  repeated Deployment items = 2;
}

// DeploymentSpec is the specification of the desired behavior of the Deployment.
message DeploymentSpec {
  // Number of desired pods. This is a pointer to distinguish between explicit
  // zero and not specified. Defaults to 1.
  // +optional
  optional int32 replicas = 1;

  // Label selector for pods. Existing ReplicaSets whose pods are
  // selected by this will be the ones affected by this deployment.
  // It must match the pod template's labels.
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;

  // Template describes the pods that will be created.
  // The only allowed template.spec.restartPolicy value is "Always".
  optional k8s.io.api.core.v1.PodTemplateSpec template = 3;

  // The deployment strategy to use to replace existing pods with new ones.
  // +optional
  // +patchStrategy=retainKeys
  optional DeploymentStrategy strategy = 4;

  // Minimum number of seconds for which a newly created pod should be ready
  // without any of its container crashing, for it to be considered available.
  // Defaults to 0 (pod will be considered available as soon as it is ready)
  // +optional
  optional int32 minReadySeconds = 5;

  // The number of old ReplicaSets to retain to allow rollback.
  // This is a pointer to distinguish between explicit zero and not specified.
  // Defaults to 10.
  // +optional
  optional int32 revisionHistoryLimit = 6;

  // Indicates that the deployment is paused.
  // +optional
  optional bool paused = 7;

  // The maximum time in seconds for a deployment to make progress before it
  // is considered to be failed. The deployment controller will continue to
  // process failed deployments and a condition with a ProgressDeadlineExceeded
  // reason will be surfaced in the deployment status. Note that progress will
  // not be estimated during the time a deployment is paused. Defaults to 600s.
  optional int32 progressDeadlineSeconds = 9;
}

// DeploymentStatus is the most recently observed status of the Deployment.
message DeploymentStatus {
  // The generation observed by the deployment controller.
  // +optional
  optional int64 observedGeneration = 1;

  // Total number of non-terminated pods targeted by this deployment (their labels match the selector).
  // +optional
  optional int32 replicas = 2;

  // Total number of non-terminated pods targeted by this deployment that have the desired template spec.
  // +optional
  optional int32 updatedReplicas = 3;

  // readyReplicas is the number of pods targeted by this Deployment with a Ready Condition.
  // +optional
  optional int32 readyReplicas = 7;

  // Total number of available pods (ready for at least minReadySeconds) targeted by this deployment.
  // +optional
  optional int32 availableReplicas = 4;

  // Total number of unavailable pods targeted by this deployment. This is the total number of
  // pods that are still required for the deployment to have 100% available capacity. They may
  // either be pods that are running but not yet available or pods that still have not been created.
  // +optional
  optional int32 unavailableReplicas = 5;

  // Represents the latest available observations of a deployment's current state.
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated DeploymentCondition conditions = 6;

  // Count of hash collisions for the Deployment. The Deployment controller uses this
  // field as a collision avoidance mechanism when it needs to create the name for the
  // newest ReplicaSet.
  // +optional
  optional int32 collisionCount = 8;
}

// DeploymentStrategy describes how to replace existing pods with new ones.
message DeploymentStrategy {
  // Type of deployment. Can be "Recreate" or "RollingUpdate". Default is RollingUpdate.
  // +optional
  optional string type = 1;

  // Rolling update config params. Present only if DeploymentStrategyType =
  // RollingUpdate.
  // ---
  // TODO: Update this to follow our convention for oneOf, whatever we decide it
  // to be.
  // +optional
  optional RollingUpdateDeployment rollingUpdate = 2;
}

// ReplicaSet ensures that a specified number of pod replicas are running at any given time.
message ReplicaSet {
  // If the Labels of a ReplicaSet are empty, they are defaulted to
  // be the same as the Pod(s) that the ReplicaSet manages.
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the specification of the desired behavior of the ReplicaSet.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ReplicaSetSpec spec = 2;

  // Status is the most recently observed status of the ReplicaSet.
  // This data may be out of date by some window of time.
  // Populated by the system.
  // Read-only.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ReplicaSetStatus status = 3;
}

// ReplicaSetCondition describes the state of a replica set at a certain point.
message ReplicaSetCondition {
  // Type of replica set condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // The last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // The reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // A human readable message indicating details about the transition.
  // +optional
  optional string message = 5;
}

// ReplicaSetList is a collection of ReplicaSets.
message ReplicaSetList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of ReplicaSets.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller
  repeated ReplicaSet items = 2;
}

// ReplicaSetSpec is the specification of a ReplicaSet.
message ReplicaSetSpec {
  // Replicas is the number of desired replicas.
  // This is a pointer to distinguish between explicit zero and unspecified.
  // Defaults to 1.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller/#what-is-a-replicationcontroller
  // +optional
  optional int32 replicas = 1;

  // Minimum number of seconds for which a newly created pod should be ready
  // without any of its container crashing, for it to be considered available.
  // Defaults to 0 (pod will be considered available as soon as it is ready)
  // +optional
  optional int32 minReadySeconds = 4;

  // Selector is a label query over pods that should match the replica count.
  // Label keys and values that must match in order to be controlled by this replica set.
  // It must match the pod template's labels.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;

  // Template is the object that describes the pod that will be created if
  // insufficient replicas are detected.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller#pod-template
  // +optional
  optional k8s.io.api.core.v1.PodTemplateSpec template = 3;
}

// ReplicaSetStatus represents the current status of a ReplicaSet.
message ReplicaSetStatus {
  // Replicas is the most recently observed number of replicas.
  // More info: https://kubernetes.io/docs/concepts/workloads/controllers/replicationcontroller/#what-is-a-replicationcontroller
  optional int32 replicas = 1;

  // The number of pods that have labels matching the labels of the pod template of the replicaset.
  // +optional
  optional int32 fullyLabeledReplicas = 2;

  // readyReplicas is the number of pods targeted by this ReplicaSet with a Ready Condition.
  // +optional
  optional int32 readyReplicas = 4;

  // The number of available replicas (ready for at least minReadySeconds) for this replica set.
  // +optional
  optional int32 availableReplicas = 5;

  // ObservedGeneration reflects the generation of the most recently observed ReplicaSet.
  // +optional
  optional int64 observedGeneration = 3;

  // Represents the latest available observations of a replica set's current state.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated ReplicaSetCondition conditions = 6;
}

// Spec to control the desired behavior of daemon set rolling update.
message RollingUpdateDaemonSet {
  // The maximum number of DaemonSet pods that can be unavailable during the
  // update. Value can be an absolute number (ex: 5) or a percentage of total
  // number of DaemonSet pods at the start of the update (ex: 10%). Absolute
  // number is calculated from percentage by rounding up.
  // This cannot be 0 if MaxSurge is 0
  // Default value is 1.
  // Example: when this is set to 30%, at most 30% of the total number of nodes
  // that should be running the daemon pod (i.e. status.desiredNumberScheduled)
  // can have their pods stopped for an update at any given time. The update
  // starts by stopping at most 30% of those DaemonSet pods and then brings
  // up new DaemonSet pods in their place. Once the new pods are available,
  // it then proceeds onto other DaemonSet pods, thus ensuring that at least
  // 70% of original number of DaemonSet pods are available at all times during
  // the update.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxUnavailable = 1;

  // The maximum number of nodes with an existing available DaemonSet pod that
  // can have an updated DaemonSet pod during during an update.
  // Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
  // This can not be 0 if MaxUnavailable is 0.
  // Absolute number is calculated from percentage by rounding up to a minimum of 1.
  // Default value is 0.
  // Example: when this is set to 30%, at most 30% of the total number of nodes
  // that should be running the daemon pod (i.e. status.desiredNumberScheduled)
  // can have their a new pod created before the old pod is marked as deleted.
  // The update starts by launching new pods on 30% of nodes. Once an updated
  // pod is available (Ready for at least minReadySeconds) the old DaemonSet pod
  // on that node is marked deleted. If the old pod becomes unavailable for any
  // reason (Ready transitions to false, is evicted, or is drained) an updated
  // pod is immediatedly created on that node without considering surge limits.
  // Allowing surge implies the possibility that the resources consumed by the
  // daemonset on any given node can double if the readiness check fails, and
  // so resource intensive daemonsets should take into account that they may
  // cause evictions during disruption.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxSurge = 2;
}

// Spec to control the desired behavior of rolling update.
message RollingUpdateDeployment {
  // The maximum number of pods that can be unavailable during the update.
  // Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
  // Absolute number is calculated from percentage by rounding down.
  // This can not be 0 if MaxSurge is 0.
  // Defaults to 25%.
  // Example: when this is set to 30%, the old ReplicaSet can be scaled down to 70% of desired pods
  // immediately when the rolling update starts. Once new pods are ready, old ReplicaSet
  // can be scaled down further, followed by scaling up the new ReplicaSet, ensuring
  // that the total number of pods available at all times during the update is at
  // least 70% of desired pods.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxUnavailable = 1;

  // The maximum number of pods that can be scheduled above the desired number of
  // pods.
  // Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
  // This can not be 0 if MaxUnavailable is 0.
  // Absolute number is calculated from percentage by rounding up.
  // Defaults to 25%.
  // Example: when this is set to 30%, the new ReplicaSet can be scaled up immediately when
  // the rolling update starts, such that the total number of old and new pods do not exceed
  // 130% of desired pods. Once old pods have been killed,
  // new ReplicaSet can be scaled up further, ensuring that total number of pods running
  // at any time during the update is at most 130% of desired pods.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxSurge = 2;
}

// RollingUpdateStatefulSetStrategy is used to communicate parameter for RollingUpdateStatefulSetStrategyType.
message RollingUpdateStatefulSetStrategy {
  // Partition indicates the ordinal at which the StatefulSet should be partitioned
  // for updates. During a rolling update, all pods from ordinal Replicas-1 to
  // Partition are updated. All pods from ordinal Partition-1 to 0 remain untouched.
  // This is helpful in being able to do a canary based deployment. The default value is 0.
  // +optional
  optional int32 partition = 1;

  // The maximum number of pods that can be unavailable during the update.
  // Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
  // Absolute number is calculated from percentage by rounding up. This can not be 0.
  // Defaults to 1. This field is alpha-level and is only honored by servers that enable the
  // MaxUnavailableStatefulSet feature. The field applies to all pods in the range 0 to
  // Replicas-1. That means if there is any unavailable pod in the range 0 to Replicas-1, it
  // will be counted towards MaxUnavailable.
  // +optional
  optional k8s.io.apimachinery.pkg.util.intstr.IntOrString maxUnavailable = 2;
}

// StatefulSet represents a set of pods with consistent identities.
// Identities are defined as:
//   - Network: A single stable DNS and hostname.
//   - Storage: As many VolumeClaims as requested.
//
// The StatefulSet guarantees that a given network identity will always
// map to the same storage identity.
message StatefulSet {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec defines the desired identities of pods in this set.
  // +optional
  optional StatefulSetSpec spec = 2;

  // Status is the current status of Pods in this StatefulSet. This data
  // may be out of date by some window of time.
  // +optional
  optional StatefulSetStatus status = 3;
}

// StatefulSetCondition describes the state of a statefulset at a certain point.
message StatefulSetCondition {
  // Type of statefulset condition.
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  optional string status = 2;

  // Last time the condition transitioned from one status to another.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 3;

  // The reason for the condition's last transition.
  // +optional
  optional string reason = 4;

  // A human readable message indicating details about the transition.
  // +optional
  optional string message = 5;
}

// StatefulSetList is a collection of StatefulSets.
message StatefulSetList {
  // Standard list's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is the list of stateful sets.
  repeated StatefulSet items = 2;
}

// StatefulSetOrdinals describes the policy used for replica ordinal assignment
// in this StatefulSet.
message StatefulSetOrdinals {
  // start is the number representing the first replica's index. It may be used
  // to number replicas from an alternate index (eg: 1-indexed) over the default
  // 0-indexed names, or to orchestrate progressive movement of replicas from
  // one StatefulSet to another.
  // If set, replica indices will be in the range:
  //   [.spec.ordinals.start, .spec.ordinals.start + .spec.replicas).
  // If unset, defaults to 0. Replica indices will be in the range:
  //   [0, .spec.replicas).
  // +optional
  optional int32 start = 1;
}

// StatefulSetPersistentVolumeClaimRetentionPolicy describes the policy used for PVCs
// created from the StatefulSet VolumeClaimTemplates.
message StatefulSetPersistentVolumeClaimRetentionPolicy {
  // WhenDeleted specifies what happens to PVCs created from StatefulSet
  // VolumeClaimTemplates when the StatefulSet is deleted. The default policy
  // of `Retain` causes PVCs to not be affected by StatefulSet deletion. The
  // `Delete` policy causes those PVCs to be deleted.
  optional string whenDeleted = 1;

  // WhenScaled specifies what happens to PVCs created from StatefulSet
  // VolumeClaimTemplates when the StatefulSet is scaled down. The default
  // policy of `Retain` causes PVCs to not be affected by a scaledown. The
  // `Delete` policy causes the associated PVCs for any excess pods above
  // the replica count to be deleted.
  optional string whenScaled = 2;
}

// A StatefulSetSpec is the specification of a StatefulSet.
message StatefulSetSpec {
  // replicas is the desired number of replicas of the given Template.
  // These are replicas in the sense that they are instantiations of the
  // same Template, but individual replicas also have a consistent identity.
  // If unspecified, defaults to 1.
  // TODO: Consider a rename of this field.
  // +optional
  optional int32 replicas = 1;

  // selector is a label query over pods that should match the replica count.
  // It must match the pod template's labels.
  // More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/#label-selectors
  optional k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector selector = 2;

  // template is the object that describes the pod that will be created if
  // insufficient replicas are detected. Each pod stamped out by the StatefulSet
  // will fulfill this Template, but have a unique identity from the rest
  // of the StatefulSet. Each pod will be named with the format
  // <statefulsetname>-<podindex>. For example, a pod in a StatefulSet named
  // "web" with index number "3" would be named "web-3".
  // The only allowed template.spec.restartPolicy value is "Always".
  optional k8s.io.api.core.v1.PodTemplateSpec template = 3;

  // volumeClaimTemplates is a list of claims that pods are allowed to reference.
  // The StatefulSet controller is responsible for mapping network identities to
  // claims in a way that maintains the identity of a pod. Every claim in
  // this list must have at least one matching (by name) volumeMount in one
  // container in the template. A claim in this list takes precedence over
  // any volumes in the template, with the same name.
  // TODO: Define the behavior if a claim already exists with the same name.
  // +optional
  repeated k8s.io.api.core.v1.PersistentVolumeClaim volumeClaimTemplates = 4;

  // serviceName is the name of the service that governs this StatefulSet.
  // This service must exist before the StatefulSet, and is responsible for
  // the network identity of the set. Pods get DNS/hostnames that follow the
  // pattern: pod-specific-string.serviceName.default.svc.cluster.local
  // where "pod-specific-string" is managed by the StatefulSet controller.
  optional string serviceName = 5;

  // podManagementPolicy controls how pods are created during initial scale up,
  // when replacing pods on nodes, or when scaling down. The default policy is
  // `OrderedReady`, where pods are created in increasing order (pod-0, then
  // pod-1, etc) and the controller will wait until each pod is ready before
  // continuing. When scaling down, the pods are removed in the opposite order.
  // The alternative policy is `Parallel` which will create pods in parallel
  // to match the desired scale without waiting, and on scale down will delete
  // all pods at once.
  // +optional
  optional string podManagementPolicy = 6;

  // updateStrategy indicates the StatefulSetUpdateStrategy that will be
  // employed to update Pods in the StatefulSet when a revision is made to
  // Template.
  optional StatefulSetUpdateStrategy updateStrategy = 7;

  // revisionHistoryLimit is the maximum number of revisions that will
  // be maintained in the StatefulSet's revision history. The revision history
  // consists of all revisions not represented by a currently applied
  // StatefulSetSpec version. The default value is 10.
  optional int32 revisionHistoryLimit = 8;

  // Minimum number of seconds for which a newly created pod should be ready
  // without any of its container crashing for it to be considered available.
  // Defaults to 0 (pod will be considered available as soon as it is ready)
  // +optional
  optional int32 minReadySeconds = 9;

  // persistentVolumeClaimRetentionPolicy describes the lifecycle of persistent
  // volume claims created from volumeClaimTemplates. By default, all persistent
  // volume claims are created as needed and retained until manually deleted. This
  // policy allows the lifecycle to be altered, for example by deleting persistent
  // volume claims when their stateful set is deleted, or when their pod is scaled
  // down. This requires the StatefulSetAutoDeletePVC feature gate to be enabled,
  // which is alpha.  +optional
  optional StatefulSetPersistentVolumeClaimRetentionPolicy persistentVolumeClaimRetentionPolicy = 10;

  // ordinals controls the numbering of replica indices in a StatefulSet. The
  // default ordinals behavior assigns a "0" index to the first replica and
  // increments the index by one for each additional replica requested. Using
  // the ordinals field requires the StatefulSetStartOrdinal feature gate to be
  // enabled, which is beta.
  // +optional
  optional StatefulSetOrdinals ordinals = 11;
}

// StatefulSetStatus represents the current state of a StatefulSet.
message StatefulSetStatus {
  // observedGeneration is the most recent generation observed for this StatefulSet. It corresponds to the
  // StatefulSet's generation, which is updated on mutation by the API Server.
  // +optional
  optional int64 observedGeneration = 1;

  // replicas is the number of Pods created by the StatefulSet controller.
  optional int32 replicas = 2;

  // readyReplicas is the number of pods created for this StatefulSet with a Ready Condition.
  optional int32 readyReplicas = 3;

  // currentReplicas is the number of Pods created by the StatefulSet controller from the StatefulSet version
  // indicated by currentRevision.
  optional int32 currentReplicas = 4;

  // updatedReplicas is the number of Pods created by the StatefulSet controller from the StatefulSet version
  // indicated by updateRevision.
  optional int32 updatedReplicas = 5;

  // currentRevision, if not empty, indicates the version of the StatefulSet used to generate Pods in the
  // sequence [0,currentReplicas).
  optional string currentRevision = 6;

  // updateRevision, if not empty, indicates the version of the StatefulSet used to generate Pods in the sequence
  // [replicas-updatedReplicas,replicas)
  optional string updateRevision = 7;

  // collisionCount is the count of hash collisions for the StatefulSet. The StatefulSet controller
  // uses this field as a collision avoidance mechanism when it needs to create the name for the
  // newest ControllerRevision.
  // +optional
  optional int32 collisionCount = 9;

  // Represents the latest available observations of a statefulset's current state.
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  repeated StatefulSetCondition conditions = 10;

  // Total number of available pods (ready for at least minReadySeconds) targeted by this statefulset.
  // +optional
  optional int32 availableReplicas = 11;
}

// StatefulSetUpdateStrategy indicates the strategy that the StatefulSet
// controller will use to perform updates. It includes any additional parameters
// necessary to perform the update for the indicated strategy.
message StatefulSetUpdateStrategy {
  // Type indicates the type of the StatefulSetUpdateStrategy.
  // Default is RollingUpdate.
  // +optional
  optional string type = 1;

  // RollingUpdate is used to communicate parameters when Type is RollingUpdateStatefulSetStrategyType.
  // +optional
  optional RollingUpdateStatefulSetStrategy rollingUpdate = 2;
}

