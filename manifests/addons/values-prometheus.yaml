# To simplify the deployment, disable non-essential components
alertmanager:
  enabled: false
prometheus-pushgateway:
  enabled: false
kube-state-metrics:
  enabled: false
prometheus-node-exporter:
  enabled: false
server:
  podLabels:
    sidecar.istio.io/inject: "false"
  persistentVolume:
    enabled: false
  # Use port 9090 to match Istio documentation
  service:
    servicePort: 9090
  readinessProbeInitialDelay: 0
  # Speed up scraping a bit from the default
  global:
    scrape_interval: 15s

  # Match legacy addon deployment
  fullnameOverride: prometheus

  # use dockerhub
  image:
    repository: prom/prometheus

  securityContext: null

configmapReload:
  prometheus:
    image:
      # Use ghcr
      repository: ghcr.io/prometheus-operator/prometheus-config-reloader
