{"version": 1, "dependencies": [{"source": {"git": {"remote": "https://github.com/grafana/grafonnet.git", "subdir": "gen/grafonnet-latest"}}, "version": "18eb4e529c60ec20ab243e2da3bcb52b2fd0d223", "sum": "eyuJ0jOXeA4MrobbNgU4/v5a7ASDHslHZ0eS6hDdWoI="}, {"source": {"git": {"remote": "https://github.com/grafana/grafonnet.git", "subdir": "gen/grafonnet-v11.0.0"}}, "version": "18eb4e529c60ec20ab243e2da3bcb52b2fd0d223", "sum": "6ycXhBvq/k7j01vaNiDSZcVsKikh01pfNH+2oauHgJg="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/docsonnet.git", "subdir": "doc-util"}}, "version": "6ac6c69685b8c29c54515448eaca583da2d88150", "sum": "BrAL/k23jq+xy9oA7TWIhUx07dsA/QLm3g7ktCwe//U="}, {"source": {"git": {"remote": "https://github.com/jsonnet-libs/xtd.git", "subdir": ""}}, "version": "63d430b69a95741061c2f7fc9d84b1a778511d9c", "sum": "qiZi3axUSXCVzKUF83zSAxklwrnitMmrDK4XAfjPMdE="}], "legacyImports": false}