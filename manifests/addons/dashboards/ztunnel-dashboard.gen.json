{"graphTooltip": 1, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "Process", "type": "row"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Version number of each running instance", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 1}, "id": 2, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (tag) (istio_build{component=\"ztunnel\"})", "legendFormat": "Version ({{tag}})"}], "title": "Ztunnel Versions", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Memory usage of each running instance", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "bytes"}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 1}, "id": 3, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (container_memory_working_set_bytes{container=\"istio-proxy\",pod=~\"ztunnel-.*\"})", "legendFormat": "Container ({{pod}})"}], "title": "Memory Usage", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "CPU usage of each running instance", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 1}, "id": 4, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (irate(container_cpu_usage_seconds_total{container=\"istio-proxy\",pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "Container ({{pod}})"}], "title": "CPU Usage", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 5, "panels": [], "title": "Network", "type": "row"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Connections opened and closed per instance", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "cps"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}, "id": 6, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (rate(istio_tcp_connections_opened_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "Opened ({{pod}})"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "-sum by (pod) (rate(istio_tcp_connections_closed_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "Closed ({{pod}})"}], "title": "Connections", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Bytes sent and received per instance", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "Bps"}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 10}, "id": 7, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (rate(istio_tcp_sent_bytes_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "Sent ({{pod}})"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (rate(istio_tcp_received_bytes_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "Received ({{pod}})"}], "title": "Bytes Transmitted", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "DNS queries received per instance", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "qps"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 10}, "id": 8, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (rate(istio_dns_requests_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "Request ({{pod}})"}], "title": "DNS Request", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 9, "panels": [], "title": "Operations", "type": "row"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Count of XDS connection terminations.\nThis will typically spike every 30min for each instance.\n", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}, "id": 10, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (rate(istio_xds_connection_terminations_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "XDS Connection Terminations ({{pod}})"}], "title": "XDS Connections", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "showPoints": "never", "stacking": {"mode": "normal"}}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cds"}, "properties": [{"id": "displayName", "value": "Clusters"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "eds"}, "properties": [{"id": "displayName", "value": "Endpoints"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "lds"}, "properties": [{"id": "displayName", "value": "Listeners"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "rds"}, "properties": [{"id": "displayName", "value": "Routes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "nds"}, "properties": [{"id": "displayName", "value": "DNS Tables"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "istio.io/debug"}, "properties": [{"id": "displayName", "value": "Debug"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "istio.io/debug/syncz"}, "properties": [{"id": "displayName", "value": "Debug"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "wads"}, "properties": [{"id": "displayName", "value": "Authorization"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "wds"}, "properties": [{"id": "displayName", "value": "Workloads"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "type.googleapis.com/istio.security.Authorization"}, "properties": [{"id": "displayName", "value": "Authorizations"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "type.googleapis.com/istio.workload.Address"}, "properties": [{"id": "displayName", "value": "Addresses"}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}, "id": 11, "interval": "15s", "options": {"legend": {"calcs": [], "displayMode": "list"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (url) (irate(istio_xds_message_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))", "legendFormat": "{{url}}"}], "title": "XDS Pushes", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Count of active and pending proxies managed by each instance.\nPending is expected to converge to zero.\n", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}, "id": 12, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (workload_manager_active_proxy_count{pod=~\"ztunnel-.*\"})", "legendFormat": "Active Proxies ({{pod}})"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (pod) (workload_manager_pending_proxy_count{pod=~\"ztunnel-.*\"})", "legendFormat": "Pending Proxies ({{pod}})"}], "title": "Workload Manager", "type": "timeseries"}], "refresh": "15s", "schemaVersion": 39, "templating": {"list": [{"name": "datasource", "query": "prometheus", "type": "datasource"}]}, "time": {"from": "now-30m", "to": "now"}, "timezone": "utc", "title": "<PERSON><PERSON><PERSON> Dashboard", "uid": "12c58766acc81a1c835dd5059eaf2741"}