{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 21, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "Performance Dashboard Notes", "type": "row"}, {"gridPos": {"h": 6, "w": 24, "x": 0, "y": 1}, "id": 19, "links": [], "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "The charts on this dashboard are intended to show Istio main components cost in terms of resources utilization under steady load.\n\n- **vCPU / 1k rps:** shows vCPU utilization by the main Istio components normalized by 1000 requests/second. When idle or low traffic, this chart will be blank. The curve for istio-proxy refers to the services sidecars only.\n- **vCPU:** vCPU utilization by Istio components, not normalized.\n- **Memory:** memory footprint for the components. Telemetry and policy are normalized by 1k rps, and no data is shown  when there is no traffic. For ingress and istio-proxy, the data is per instance.\n- **Bytes transferred / sec:** shows the number of bytes flowing through each Istio component.\n\n\n", "mode": "markdown"}, "pluginVersion": "10.1.5", "title": "Performance Dashboard README", "transparent": true, "type": "text"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 6, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "vCPU Usage", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 4, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "(sum(irate(container_cpu_usage_seconds_total{pod=~\"istio-ingressgateway-.*\",container=\"istio-proxy\"}[$__rate_interval])) / (round(sum(irate(istio_requests_total{source_workload=\"istio-ingressgateway\", reporter=\"source\"}[$__rate_interval])), 0.001)/1000))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "istio-ingressgateway", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "(sum(irate(container_cpu_usage_seconds_total{namespace!=\"istio-system\",container=\"istio-proxy\"}[$__rate_interval]))/ (round(sum(irate(istio_requests_total[$__rate_interval])), 0.001)/1000))/ (sum(irate(istio_requests_total{source_workload=\"istio-ingressgateway\"}[$__rate_interval])) >bool 10)", "format": "time_series", "intervalFactor": 1, "legendFormat": "istio-proxy", "refId": "B"}], "title": "vCPU / 1k rps", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 7, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(rate(container_cpu_usage_seconds_total{pod=~\"istio-ingressgateway-.*\",container=\"istio-proxy\"}[$__rate_interval]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "istio-ingressgateway", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(rate(container_cpu_usage_seconds_total{namespace!=\"istio-system\",container=\"istio-proxy\"}[$__rate_interval]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "istio-proxy", "refId": "B"}], "title": "vCPU", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 13, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "Memory and Data Rates", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 902, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(container_memory_working_set_bytes{pod=~\"istio-ingressgateway-.*\"}) / count(container_memory_working_set_bytes{pod=~\"istio-ingressgateway-.*\",container!=\"POD\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "per istio-ingressgateway", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(container_memory_working_set_bytes{namespace!=\"istio-system\",container=\"istio-proxy\"}) / count(container_memory_working_set_bytes{namespace!=\"istio-system\",container=\"istio-proxy\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "per istio proxy", "refId": "B"}], "title": "Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 11, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(irate(istio_response_bytes_sum{source_workload=\"istio-ingressgateway\", reporter=\"source\"}[$__rate_interval]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "istio-ingressgateway", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(irate(istio_response_bytes_sum{source_workload_namespace!=\"istio-system\", reporter=\"source\"}[$__rate_interval])) + sum(irate(istio_request_bytes_sum{source_workload_namespace!=\"istio-system\", reporter=\"source\"}[$__rate_interval]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "istio-proxy", "refId": "B"}], "title": "Bytes transferred / sec", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 17, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "Istio Component Versions", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 26}, "id": 15, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(istio_build) by (component, tag)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{ component }}: {{ tag }}", "refId": "A"}], "title": "Istio Components by Version", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 34}, "id": 71, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "Proxy Resource Usage", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 35}, "id": 72, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(container_memory_working_set_bytes{container=\"istio-proxy\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Total (k8s)", "refId": "A", "step": 2}], "title": "Memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 35}, "id": 73, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(rate(container_cpu_usage_seconds_total{container=\"istio-proxy\"}[$__rate_interval]))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Total (k8s)", "refId": "A", "step": 2}], "title": "vCPU", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 35}, "id": 702, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(container_fs_usage_bytes{container=\"istio-proxy\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "Total (k8s)", "refId": "A", "step": 2}], "title": "Disk", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 42}, "id": 69, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "Istiod Resource Usage", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 43}, "id": 5, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "process_virtual_memory_bytes{app=\"istiod\"}", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "Virtual Memory", "refId": "I", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "process_resident_memory_bytes{app=\"istiod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Resident Memory", "refId": "H", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "go_memstats_heap_sys_bytes{app=\"istiod\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "heap sys", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "go_memstats_heap_alloc_bytes{app=\"istiod\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "heap alloc", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "go_memstats_alloc_bytes{app=\"istiod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Alloc", "refId": "F", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "go_memstats_heap_inuse_bytes{app=\"istiod\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Heap in-use", "refId": "E", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "go_memstats_stack_inuse_bytes{app=\"istiod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Stack in-use", "refId": "G", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(container_memory_working_set_bytes{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Total (k8s)", "refId": "C", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "container_memory_working_set_bytes{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{ container }} (k8s)", "refId": "B", "step": 2}], "title": "Memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 43}, "id": 602, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(rate(container_cpu_usage_seconds_total{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}[$__rate_interval]))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Total (k8s)", "refId": "A", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(rate(container_cpu_usage_seconds_total{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}[$__rate_interval])) by (container)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{ container }} (k8s)", "refId": "B", "step": 2}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "irate(process_cpu_seconds_total{app=\"istiod\"}[$__rate_interval])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "pilot (self-reported)", "refId": "C", "step": 2}], "title": "vCPU", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 43}, "id": 74, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "process_open_fds{app=\"istiod\"}", "format": "time_series", "hide": true, "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "Open FDs (pilot)", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "container_fs_usage_bytes{ container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ container }}", "refId": "B", "step": 2}], "title": "Disk", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 43}, "id": 402, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "go_goroutines{app=\"istiod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Number of Goroutines", "refId": "A", "step": 2}], "title": "Goroutines", "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"hide": 0, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Istio Performance Dashboard", "version": 1, "weekStart": ""}