{"graphTooltip": 1, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "Global Traffic", "type": "row"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Total requests in the cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "reqps"}}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 1}, "id": 2, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "round(sum (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval])), 0.01)"}], "title": "Traffic Volume", "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Total success rate of requests in the cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "percentunit"}}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 1}, "id": 3, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code!~\"5..\"}[$__rate_interval])) / sum (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval]))"}], "title": "Success Rate", "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Total 4xx requests in in the cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "reqps"}}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 1}, "id": 4, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "round(sum (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code=~\"4..\"}[$__rate_interval])), 0.01)or vector(0)"}], "title": "4xxs", "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Total 5xx requests in in the cluster", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}, "unit": "reqps"}}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 1}, "id": 5, "interval": "5s", "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "round(sum (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code=~\"5..\"}[$__rate_interval])), 0.01)or vector(0)"}], "title": "5xxs", "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Request information for HTTP services", "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #requests"}, "properties": [{"id": "displayName", "value": "Requests"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "reqps"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #p50"}, "properties": [{"id": "displayName", "value": "P50 Latency"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "ms"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #p90"}, "properties": [{"id": "displayName", "value": "P90 Latency"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "ms"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #p99"}, "properties": [{"id": "displayName", "value": "P99 Latency"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "ms"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #success"}, "properties": [{"id": "displayName", "value": "Success Rate"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": "0.95"}, {"color": "green", "value": 1}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_workload_var"}, "properties": [{"id": "displayName", "value": "Workload"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_service"}, "properties": [{"id": "displayName", "value": "Service"}, {"id": "custom.minWidth", "value": 400}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_workload_namespace"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_workload"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.hidden", "value": true}]}]}, "gridPos": {"h": 16, "w": 24, "y": 10}, "id": 6, "interval": "5s", "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "label_join(sum by (destination_workload,destination_workload_namespace,destination_service) (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")", "format": "table", "instant": true, "legendFormat": "{{ destination_workload}}.{{ destination_workload_namespace }}", "refId": "requests"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "label_join(histogram_quantile(0.5, sum by (le,destination_workload,destination_workload_namespace) (rate(istio_request_duration_milliseconds_bucket{reporter=~\"source|waypoint\"}[$__rate_interval]))), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")", "format": "table", "instant": true, "legendFormat": "{{ destination_workload}}.{{ destination_workload_namespace }}", "refId": "p50"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "label_join(histogram_quantile(0.9, sum by (le,destination_workload,destination_workload_namespace) (rate(istio_request_duration_milliseconds_bucket{reporter=~\"source|waypoint\"}[$__rate_interval]))), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")", "format": "table", "instant": true, "legendFormat": "{{ destination_workload}}.{{ destination_workload_namespace }}", "refId": "p90"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "label_join(histogram_quantile(0.99, sum by (le,destination_workload,destination_workload_namespace) (rate(istio_request_duration_milliseconds_bucket{reporter=~\"source|waypoint\"}[$__rate_interval]))), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")", "format": "table", "instant": true, "legendFormat": "{{ destination_workload}}.{{ destination_workload_namespace }}", "refId": "p99"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "label_join(sum by (destination_workload,destination_workload_namespace) (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code!~\"5..\"}[$__rate_interval]))/sum by (destination_workload,destination_workload_namespace) (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")", "format": "table", "instant": true, "legendFormat": "{{ destination_workload}}.{{ destination_workload_namespace }}", "refId": "success"}], "title": "HTTP/gRPC Workloads", "transformations": [{"id": "merge"}], "type": "table"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Bytes sent and recieived information for TCP services", "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #recv"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON> Received"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "bps"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #sent"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON>"}, {"id": "decimals", "value": 2}, {"id": "unit", "value": "bps"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_workload_var"}, "properties": [{"id": "displayName", "value": "Workload"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_service"}, "properties": [{"id": "displayName", "value": "Service"}, {"id": "custom.minWidth", "value": 400}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_workload_namespace"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "destination_workload"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.hidden", "value": true}]}]}, "gridPos": {"h": 16, "w": 24, "y": 26}, "id": 7, "interval": "5s", "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "label_join(sum by (destination_workload,destination_workload_namespace,destination_service) (rate(istio_tcp_received_bytes_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")", "format": "table", "instant": true, "legendFormat": "{{ destination_workload}}.{{ destination_workload_namespace }}", "refId": "recv"}, {"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "label_join(sum by (destination_workload,destination_workload_namespace,destination_service) (rate(istio_tcp_sent_bytes_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")", "format": "table", "instant": true, "legendFormat": "{{ destination_workload}}.{{ destination_workload_namespace }}", "refId": "sent"}], "title": "TCP Workloads", "transformations": [{"id": "merge"}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 42}, "id": 8, "panels": [], "title": "Istio Component Versions", "type": "row"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "Version number of each running instance", "fieldConfig": {"defaults": {"custom": {"fillOpacity": 10, "gradientMode": "hue", "showPoints": "never"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 43}, "id": 9, "interval": "5s", "options": {"legend": {"calcs": [], "displayMode": "list"}}, "pluginVersion": "v11.0.0", "targets": [{"datasource": {"type": "prometheus", "uid": "$datasource"}, "expr": "sum by (component,tag) (istio_build)", "legendFormat": "{{component}} ({{tag}})"}], "title": "Istio Component Versions", "type": "timeseries"}], "refresh": "15s", "schemaVersion": 39, "templating": {"list": [{"name": "datasource", "query": "prometheus", "type": "datasource"}]}, "time": {"from": "now-30m", "to": "now"}, "timezone": "utc", "title": "<PERSON><PERSON><PERSON> Dashboard", "uid": "1a9a8ea49444aae205c7737573e894f9"}