{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 3, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "Wasm VMs", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_envoy_wasm_runtime_null_active)", "interval": "", "legendFormat": "native", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_envoy_wasm_runtime_v8_active)", "interval": "", "legendFormat": "v8", "refId": "B"}], "title": "Active", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_envoy_wasm_runtime_null_created)", "interval": "", "legendFormat": "native", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_envoy_wasm_runtime_v8_created)", "interval": "", "legendFormat": "v8", "refId": "B"}], "title": "Created", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 7, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "<PERSON><PERSON> Module Remote Load", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_remote_load_cache_entries)", "interval": "", "legendFormat": "entries", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 10}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_remote_load_cache_hits)", "interval": "", "legendFormat": "hits", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_remote_load_cache_misses)", "interval": "", "legendFormat": "misses", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_remote_load_cache_negative_hits)", "interval": "", "legendFormat": "negative hits", "refId": "C"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 10}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_remote_load_fetch_failures)", "interval": "", "legendFormat": "failures", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "avg(envoy_wasm_remote_load_fetch_successes)", "interval": "", "legendFormat": "successes", "refId": "B"}], "title": "Remote Fetch", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 71, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "refId": "A"}], "title": "Proxy Resource Usage", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 72, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(container_memory_working_set_bytes{container=\"istio-proxy\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Total (k8s)", "refId": "A", "step": 2}], "title": "Memory", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}, "id": 73, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "expr": "sum(rate(container_cpu_usage_seconds_total{container=\"istio-proxy\"}[$__rate_interval]))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Total (k8s)", "refId": "A", "step": 2}], "title": "vCPU", "type": "timeseries"}], "refresh": "", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "default", "value": "default"}, "hide": 0, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "<PERSON><PERSON>o Wasm Extension Dashboard", "version": 1, "weekStart": ""}