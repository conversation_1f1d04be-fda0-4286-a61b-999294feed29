#!/bin/bash

# Copyright 2019 Istio Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

shopt -s globstar
set -e

SCRIPTPATH=$( cd "$(dirname "$0")" && pwd -P )
ROOTDIR=$SCRIPTPATH/..
cd "$ROOTDIR" || exit

# rely on go build cache
ISTIOCTL=bin/istioctl
go build -o $ISTIOCTL ./istioctl/cmd/istioctl

for f in samples/**/*.yaml; do
  if grep -q -e "{{" "$f" ; then
    echo "Skipping check for helm template $f"
    continue
  else
    echo "Validating $f..."
  $ISTIOCTL validate -x \
    -f "$f"
  fi
done
