{"configs": [{"@type": "type.googleapis.com/envoy.admin.v3.ClustersConfigDump", "version_info": "2024-03-04T08:37:44Z/4", "dynamic_active_clusters": [{"cluster": {"@type": "type.googleapis.com/envoy.config.cluster.v3.Cluster", "name": "inbound-vip|9999|http|ratings.default.svc.cluster.local", "type": "EDS", "transport_socket": {"name": "internal_upstream", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.transport_sockets.internal_upstream.v3.InternalUpstreamTransport", "passthrough_metadata": [{"kind": {"host": {}}, "name": "envoy.filters.listener.original_dst"}], "transport_socket": {"name": "raw_buffer", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.transport_sockets.raw_buffer.v3.RawBuffer"}}}}, "metadata": {"filter_metadata": {"istio": {"services": [{"namespace": "default", "name": "ratings", "host": "ratings.default.svc.cluster.local"}]}}}, "common_lb_config": {}}, "last_updated": "2024-03-04T08:37:44.840Z"}]}, {"@type": "type.googleapis.com/envoy.admin.v3.ListenersConfigDump", "version_info": "2024-03-04T08:37:44Z/4", "dynamic_listeners": [{"name": "connect_terminate", "active_state": {"listener": {"@type": "type.googleapis.com/envoy.config.listener.v3.Listener", "name": "connect_terminate", "address": {"socket_address": {"address": "0.0.0.0", "port_value": 15008}}, "filter_chains": [{"filters": [{"name": "envoy.filters.network.http_connection_manager", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager", "stat_prefix": "connect_terminate", "route_config": {"name": "default", "virtual_hosts": [{"name": "default", "domains": ["*"], "routes": [{"match": {"connect_matcher": {}}, "route": {"cluster": "main_internal", "upgrade_configs": [{"upgrade_type": "CONNECT", "connect_config": {}}]}}]}]}, "http_filters": [{"name": "waypoint_downstream_peer_metadata", "typed_config": {"@type": "type.googleapis.com/udpa.type.v1.TypedStruct", "type_url": "type.googleapis.com/io.istio.http.peer_metadata.Config", "value": {"downstream_discovery": [{"workload_discovery": {}}], "shared_with_upstream": true}}}, {"name": "connect_authority", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.http.set_filter_state.v3.Config", "on_request_headers": [{"object_key": "envoy.filters.listener.original_dst.local_ip", "format_string": {"text_format_source": {"inline_string": "%REQ(:AUTHORITY)%"}}, "shared_with_upstream": "ONCE"}, {"object_key": "envoy.filters.listener.original_dst.remote_ip", "format_string": {"text_format_source": {"inline_string": "%DOWNSTREAM_REMOTE_ADDRESS%"}}, "shared_with_upstream": "ONCE"}, {"object_key": "io.istio.peer_principal", "format_string": {"text_format_source": {"inline_string": "%DOWNSTREAM_PEER_URI_SAN%"}}, "shared_with_upstream": "ONCE", "factory_key": "envoy.string"}, {"object_key": "io.istio.local_principal", "format_string": {"text_format_source": {"inline_string": "%DOWNSTREAM_LOCAL_URI_SAN%"}}, "shared_with_upstream": "ONCE", "factory_key": "envoy.string"}]}}, {"name": "envoy.filters.http.router", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.http.router.v3.Router"}}], "http2_protocol_options": {"max_concurrent_streams": 100, "allow_connect": true}, "server_name": "istio-envoy", "forward_client_cert_details": "APPEND_FORWARD", "set_current_client_cert_details": {"subject": true, "dns": true, "uri": true}, "upgrade_configs": [{"upgrade_type": "CONNECT"}], "stream_idle_timeout": "0s"}}], "transport_socket": {"name": "tls", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext", "common_tls_context": {"tls_params": {"tls_minimum_protocol_version": "TLSv1_3", "tls_maximum_protocol_version": "TLSv1_3"}, "alpn_protocols": ["h2"], "tls_certificate_sds_secret_configs": [{"name": "default", "sds_config": {"api_config_source": {"api_type": "GRPC", "grpc_services": [{"envoy_grpc": {"cluster_name": "sds-grpc"}}], "set_node_on_first_message_only": true, "transport_api_version": "V3"}, "initial_fetch_timeout": "0s", "resource_api_version": "V3"}}], "combined_validation_context": {"default_validation_context": {"match_typed_subject_alt_names": [{"san_type": "URI", "matcher": {"prefix": "spiffe://cluster.local/"}}]}, "validation_context_sds_secret_config": {"name": "ROOTCA", "sds_config": {"api_config_source": {"api_type": "GRPC", "grpc_services": [{"envoy_grpc": {"cluster_name": "sds-grpc"}}], "set_node_on_first_message_only": true, "transport_api_version": "V3"}, "initial_fetch_timeout": "0s", "resource_api_version": "V3"}}}}, "require_client_certificate": true}}, "name": "default"}]}, "last_updated": "2024-03-04T08:37:44.851Z"}}, {"name": "main_internal", "active_state": {"listener": {"@type": "type.googleapis.com/envoy.config.listener.v3.Listener", "name": "main_internal", "filter_chains": [{"filters": [{"name": "envoy.filters.network.http_connection_manager", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager", "stat_prefix": "inbound_0.0.0.0_9999", "route_config": {"name": "inbound-vip|9080|http|details.default.svc.cluster.local", "virtual_hosts": [{"name": "inbound|http|9080", "domains": ["*"], "routes": [{"match": {"prefix": "/"}, "route": {"cluster": "inbound-vip|9080|http|details.default.svc.cluster.local", "timeout": "0s", "max_stream_duration": {"max_stream_duration": "0s", "grpc_timeout_header_max": "0s"}}, "decorator": {"operation": ":9080/*"}, "name": "default"}]}], "validate_clusters": false}, "http_filters": [{"name": "envoy.filters.http.grpc_stats", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.http.grpc_stats.v3.FilterConfig", "emit_filter_state": true, "stats_for_all_methods": false}}, {"name": "envoy.filters.http.fault", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.http.fault.v3.HTTPFault"}}, {"name": "envoy.filters.http.cors", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.http.cors.v3.Cors"}}, {"name": "waypoint_upstream_peer_metadata", "typed_config": {"@type": "type.googleapis.com/udpa.type.v1.TypedStruct", "type_url": "type.googleapis.com/io.istio.http.peer_metadata.Config", "value": {"upstream_discovery": [{"workload_discovery": {}}]}}}, {"name": "istio.stats", "typed_config": {"@type": "type.googleapis.com/udpa.type.v1.TypedStruct", "type_url": "type.googleapis.com/stats.PluginConfig", "value": {"reporter": "SERVER_GATEWAY"}}}, {"name": "envoy.filters.http.router", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.http.router.v3.Router"}}], "tracing": {"client_sampling": {"value": 100}, "random_sampling": {"value": 1}, "overall_sampling": {"value": 100}, "custom_tags": [{"tag": "istio.authorization.dry_run.allow_policy.name", "metadata": {"kind": {"request": {}}, "metadata_key": {"key": "envoy.filters.http.rbac", "path": [{"key": "istio_dry_run_allow_shadow_effective_policy_id"}]}}}, {"tag": "istio.authorization.dry_run.allow_policy.result", "metadata": {"kind": {"request": {}}, "metadata_key": {"key": "envoy.filters.http.rbac", "path": [{"key": "istio_dry_run_allow_shadow_engine_result"}]}}}, {"tag": "istio.authorization.dry_run.deny_policy.name", "metadata": {"kind": {"request": {}}, "metadata_key": {"key": "envoy.filters.http.rbac", "path": [{"key": "istio_dry_run_deny_shadow_effective_policy_id"}]}}}, {"tag": "istio.authorization.dry_run.deny_policy.result", "metadata": {"kind": {"request": {}}, "metadata_key": {"key": "envoy.filters.http.rbac", "path": [{"key": "istio_dry_run_deny_shadow_engine_result"}]}}}, {"tag": "istio.canonical_revision", "literal": {"value": "latest"}}, {"tag": "istio.canonical_service", "literal": {"value": "namespace-istio-waypoint"}}, {"tag": "istio.cluster_id", "literal": {"value": "Kubernetes"}}, {"tag": "istio.mesh_id", "literal": {"value": "cluster.local"}}, {"tag": "istio.namespace", "literal": {"value": "default"}}]}, "server_name": "istio-envoy", "access_log": [{"name": "envoy.access_loggers.file", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.access_loggers.file.v3.FileAccessLog", "path": "/dev/stdout", "log_format": {"text_format_source": {"inline_string": "[%START_TIME%] \"%REQ(:METHOD)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL%\" %RESPONSE_CODE% %RESPONSE_FLAGS% %RESPONSE_CODE_DETAILS% %CONNECTION_TERMINATION_DETAILS% \"%UPSTREAM_TRANSPORT_FAILURE_REASON%\" %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% \"%REQ(X-FORWARDED-FOR)%\" \"%REQ(USER-AGENT)%\" \"%REQ(X-REQUEST-ID)%\" \"%REQ(:AUTHORITY)%\" \"%UPSTREAM_HOST%\" %UPSTREAM_CLUSTER% %UPSTREAM_LOCAL_ADDRESS% %DOWNSTREAM_LOCAL_ADDRESS% %DOWNSTREAM_REMOTE_ADDRESS% %REQUESTED_SERVER_NAME% %ROUTE_NAME%\n"}}}}], "use_remote_address": false, "upgrade_configs": [{"upgrade_type": "websocket"}], "stream_idle_timeout": "0s", "normalize_path": true, "request_id_extension": {"typed_config": {"@type": "type.googleapis.com/envoy.extensions.request_id.uuid.v3.UuidRequestIdConfig", "use_request_id_for_trace_sampling": true}}, "path_with_escaped_slashes_action": "KEEP_UNCHANGED"}}], "name": "inbound-vip|9080||details.default.svc.cluster.local-http"}], "listener_filters": [{"name": "envoy.filters.listener.original_dst", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.listener.original_dst.v3.OriginalDst"}}, {"name": "envoy.filters.listener.http_inspector", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.listener.http_inspector.v3.HttpInspector"}}], "traffic_direction": "INBOUND", "internal_listener": {}, "filter_chain_matcher": {"matcher_tree": {"input": {"name": "ip", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationIPInput"}}, "custom_match": {"name": "ip", "typed_config": {"@type": "type.googleapis.com/xds.type.matcher.v3.IPMatcher", "range_matchers": [{"ranges": [{"address_prefix": "**************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"80": {"action": {"name": "inbound-vip|80||sleep.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|80||sleep.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "***********", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||details.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||details.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||ratings.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||ratings.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "*************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||reviews.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||reviews.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "**************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||productpage.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||productpage.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "**************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||details-v1.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||details-v1.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "**************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||productpage-v1.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||productpage-v1.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "**************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||ratings-v1.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||ratings-v1.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "**************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||reviews-v1.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||reviews-v1.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "*************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||reviews-v2.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||reviews-v2.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"9080": {"action": {"name": "inbound-vip|9080||reviews-v3.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|9080||reviews-v3.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "*************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"80": {"action": {"name": "inbound-vip|80||bookinfo-gateway-istio.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|80||bookinfo-gateway-istio.default.svc.cluster.local-http"}}}, "15021": {"action": {"name": "inbound-vip|15021||bookinfo-gateway-istio.default.svc.cluster.local-tcp", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|15021||bookinfo-gateway-istio.default.svc.cluster.local-tcp"}}}}}}}}}, {"ranges": [{"address_prefix": "*************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "port", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.DestinationPortInput"}}, "exact_match_map": {"map": {"8080": {"action": {"name": "inbound-vip|8080||reviews12.default.svc.cluster.local-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "inbound-vip|8080||reviews12.default.svc.cluster.local-http"}}}}}}}}}, {"ranges": [{"address_prefix": "**************", "prefix_len": 32}, {"address_prefix": "***************", "prefix_len": 32}, {"address_prefix": "***************", "prefix_len": 32}, {"address_prefix": "***************", "prefix_len": 32}, {"address_prefix": "**************", "prefix_len": 32}, {"address_prefix": "**************", "prefix_len": 32}, {"address_prefix": "***************", "prefix_len": 32}, {"address_prefix": "***************", "prefix_len": 32}], "on_match": {"matcher": {"matcher_tree": {"input": {"name": "application-protocol", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.matching.common_inputs.network.v3.ApplicationProtocolInput"}}, "exact_match_map": {"map": {"'h2c'": {"action": {"name": "direct-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "direct-http"}}}, "'http/1.1'": {"action": {"name": "direct-http", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "direct-http"}}}}}}, "on_no_match": {"action": {"name": "direct-tcp", "typed_config": {"@type": "type.googleapis.com/google.protobuf.StringValue", "value": "direct-tcp"}}}}}}]}}}}}, "last_updated": "2024-03-04T08:37:44.866Z"}}, {"name": "connect_originate", "active_state": {"listener": {"@type": "type.googleapis.com/envoy.config.listener.v3.Listener", "name": "connect_originate", "filter_chains": [{"filters": [{"name": "envoy.filters.network.tcp_proxy", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.network.tcp_proxy.v3.TcpProxy", "stat_prefix": "connect_originate", "cluster": "connect_originate", "tunneling_config": {"hostname": "%DOWNSTREAM_LOCAL_ADDRESS%"}}}]}], "use_original_dst": false, "listener_filters": [{"name": "envoy.filters.listener.original_dst", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.filters.listener.original_dst.v3.OriginalDst"}}], "internal_listener": {}}, "last_updated": "2024-03-04T08:37:44.866Z"}}]}, {"@type": "type.googleapis.com/envoy.admin.v3.RoutesConfigDump", "dynamic_route_configs": [{"route_config": {"@type": "type.googleapis.com/envoy.config.route.v3.RouteConfiguration", "name": "inbound-vip|9999|http|reviews-v3.default.svc.cluster.local", "virtual_hosts": [{"name": "inbound|http|9080", "domains": ["*"], "routes": [{"match": {"prefix": "/"}, "route": {"cluster": "inbound-vip|9080|http|reviews-v3.default.svc.cluster.local", "timeout": "0s", "max_stream_duration": {"grpc_timeout_header_max": "0s"}}, "decorator": {"operation": ":9080/*"}, "name": "default"}]}], "validate_clusters": false}, "last_updated": "2024-03-04T08:37:44.862Z"}]}]}