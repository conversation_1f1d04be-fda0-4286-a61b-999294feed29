{"configs": [{"@type": "type.googleapis.com/envoy.admin.v3.SecretsConfigDump", "dynamic_active_secrets": [{"name": "default", "last_updated": "2024-10-25T13:26:36.591Z", "secret": {"@type": "type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.Secret", "name": "default", "tls_certificate": {"certificate_chain": {"inline_bytes": "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"}, "private_key": {"inline_bytes": "W3JlZGFjdGVkXQ=="}}}}, {"name": "ROOTCA", "last_updated": "2024-10-25T13:26:36.586Z", "secret": {"@type": "type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.Secret", "name": "ROOTCA", "validation_context": {"trusted_ca": {}, "custom_validator_config": {"name": "envoy.tls.cert_validator.spiffe", "typed_config": {"@type": "type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.SPIFFECertValidatorConfig", "trust_domains": [{"name": "east.local", "trust_bundle": {"inline_bytes": "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"}}, {"name": "west.local", "trust_bundle": {"inline_bytes": "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"}}]}}}}}]}]}