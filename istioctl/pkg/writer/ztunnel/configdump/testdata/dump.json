{"workloads": {"network1/***********": {"workloadIps": ["***********"], "waypoint": {"destination": "/************", "port": 15008}, "protocol": "HBONE", "uid": "Kubernetes//Pod/bookinfo/ratings-v1-6484c4d9bb-mdxm5", "name": "ratings-v1-6484c4d9bb-mdxm5", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-ratings", "workloadName": "ratings-v1", "workloadType": "deployment", "canonicalName": "ratings", "canonicalRevision": "v1", "network": "network1", "node": "ambient-worker2", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "HBONE", "uid": "Kubernetes//Pod/default/sleep-7656cf8794-lxcmx", "name": "sleep-7656cf8794-lxcmx", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "sleep", "workloadName": "sleep", "workloadType": "deployment", "canonicalName": "sleep", "canonicalRevision": "latest", "node": "ambient-worker2", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/httpbin/httpbin-65975d4c6f-jr69n", "name": "httpbin-65975d4c6f-jr69n", "namespace": "httpbin", "trustDomain": "cluster.local", "serviceAccount": "httpbin", "workloadName": "httpbin", "workloadType": "deployment", "canonicalName": "httpbin", "canonicalRevision": "v1", "node": "ambient-worker", "status": "Healthy", "clusterId": "Kubernetes"}, "/**********": {"workloadIps": ["**********"], "protocol": "TCP", "uid": "Kubernetes//Pod/gateway-system/gateway-api-admission-server-85985d48ff-5jcvd", "name": "gateway-api-admission-server-85985d48ff-5jcvd", "namespace": "gateway-system", "trustDomain": "cluster.local", "serviceAccount": "default", "workloadName": "gateway-api-admission-server", "workloadType": "deployment", "canonicalName": "gateway-api-admission-server", "canonicalRevision": "latest", "node": "ambient-worker2", "status": "Healthy", "clusterId": "Kubernetes"}, "network2/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/istio-system/istiod-test-6bdfb786d-s58pj", "name": "istiod-test-6bdfb786d-s58pj", "namespace": "istio-system", "trustDomain": "cluster.local", "serviceAccount": "istiod-test", "workloadName": "istiod-test", "workloadType": "deployment", "canonicalName": "isti<PERSON>", "canonicalRevision": "latest", "network": "network2", "node": "ambient-worker", "status": "Healthy", "clusterId": "Kubernetes"}, "network3/***********": {"workloadIps": ["***********"], "waypoint": {"destination": "/************", "port": 15008}, "protocol": "HBONE", "uid": "Kubernetes//Pod/bookinfo/reviews-v3-5b9bd44f4-7fff4", "name": "reviews-v3-5b9bd44f4-7fff4", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-reviews", "workloadName": "reviews-v3", "workloadType": "deployment", "canonicalName": "reviews", "canonicalRevision": "v3", "network": "network3", "node": "ambient-worker", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "HBONE", "uid": "Kubernetes//Pod/default/details-v1-698d88b-krdw7", "name": "details-v1-698d88b-krdw7", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-details", "workloadName": "details-v1", "workloadType": "deployment", "canonicalName": "details", "canonicalRevision": "v1", "node": "ambient-worker2", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/sleep/sleep-7656cf8794-qpvbm", "name": "sleep-7656cf8794-qpvbm", "namespace": "sleep", "trustDomain": "cluster.local", "serviceAccount": "sleep", "workloadName": "sleep", "workloadType": "deployment", "canonicalName": "sleep", "canonicalRevision": "latest", "node": "ambient-worker", "status": "Healthy", "clusterId": "Kubernetes"}, "/**********": {"workloadIps": ["**********"], "protocol": "TCP", "uid": "Kubernetes//Pod/istio-system/ztunnel-n5bg2", "name": "ztunnel-n5bg2", "namespace": "istio-system", "trustDomain": "cluster.local", "serviceAccount": "ztunnel", "workloadName": "ztunnel-n5bg2", "workloadType": "pod", "canonicalName": "ztunnel", "canonicalRevision": "latest", "node": "ambient-control-plane", "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "HBONE", "uid": "Kubernetes//Pod/default/ratings-v1-6484c4d9bb-8xc2r", "name": "ratings-v1-6484c4d9bb-8xc2r", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-ratings", "workloadName": "ratings-v1", "workloadType": "deployment", "canonicalName": "ratings", "canonicalRevision": "v1", "node": "ambient-worker2", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "waypoint": {"destination": "/***********", "port": 15008}, "protocol": "HBONE", "uid": "Kubernetes//Pod/bookinfo/productpage-v1-675fc69cf-jscn2", "name": "productpage-v1-675fc69cf-jscn2", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-productpage", "workloadName": "productpage-v1", "workloadType": "deployment", "canonicalName": "productpage", "canonicalRevision": "v1", "node": "ambient-worker2", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/istio-system/ztunnel-qk2pp", "name": "ztunnel-qk2pp", "namespace": "istio-system", "trustDomain": "cluster.local", "serviceAccount": "ztunnel", "workloadName": "ztunnel-qk2pp", "workloadType": "pod", "canonicalName": "ztunnel", "canonicalRevision": "latest", "node": "ambient-worker2", "status": "Healthy", "clusterId": "Kubernetes"}, "/**********": {"workloadIps": ["**********"], "protocol": "TCP", "uid": "Kubernetes//Pod/kube-system/coredns-5dd5756b68-mgjn9", "name": "coredns-5dd5756b68-mgjn9", "namespace": "kube-system", "trustDomain": "cluster.local", "serviceAccount": "coredns", "workloadName": "coredns", "workloadType": "deployment", "canonicalName": "coredns", "canonicalRevision": "latest", "node": "ambient-control-plane", "status": "Healthy", "clusterId": "Kubernetes"}, "/**********": {"workloadIps": ["**********"], "protocol": "TCP", "uid": "Kubernetes//Pod/kube-system/coredns-5dd5756b68-nzlpw", "name": "coredns-5dd5756b68-nzlpw", "namespace": "kube-system", "trustDomain": "cluster.local", "serviceAccount": "coredns", "workloadName": "coredns", "workloadType": "deployment", "canonicalName": "coredns", "canonicalRevision": "latest", "node": "ambient-control-plane", "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "HBONE", "uid": "Kubernetes//Pod/default/reviews-v3-5b9bd44f4-z9ms4", "name": "reviews-v3-5b9bd44f4-z9ms4", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-reviews", "workloadName": "reviews-v3", "workloadType": "deployment", "canonicalName": "reviews", "canonicalRevision": "v3", "node": "ambient-worker", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/**********": {"workloadIps": ["**********"], "protocol": "TCP", "uid": "Kubernetes//Pod/local-path-storage/local-path-provisioner-6f8956fb48-vvnpn", "name": "local-path-provisioner-6f8956fb48-vvnpn", "namespace": "local-path-storage", "trustDomain": "cluster.local", "serviceAccount": "local-path-provisioner-service-account", "workloadName": "local-path-provisioner", "workloadType": "deployment", "canonicalName": "local-path-provisioner", "canonicalRevision": "latest", "node": "ambient-control-plane", "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/istio-system/ztunnel-xljhg", "name": "ztunnel-xljhg", "namespace": "istio-system", "trustDomain": "cluster.local", "serviceAccount": "ztunnel", "workloadName": "ztunnel-xljhg", "workloadType": "pod", "canonicalName": "ztunnel", "canonicalRevision": "latest", "node": "ambient-worker", "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "waypoint": {"destination": "/************", "port": 15008}, "protocol": "HBONE", "uid": "Kubernetes//Pod/bookinfo/reviews-v2-5b667bcbf8-q5pn2", "name": "reviews-v2-5b667bcbf8-q5pn2", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-reviews", "workloadName": "reviews-v2", "workloadType": "deployment", "canonicalName": "reviews", "canonicalRevision": "v2", "node": "ambient-worker", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "HBONE", "uid": "Kubernetes//Pod/default/reviews-v2-5b667bcbf8-twvx6", "name": "reviews-v2-5b667bcbf8-twvx6", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-reviews", "workloadName": "reviews-v2", "workloadType": "deployment", "canonicalName": "reviews", "canonicalRevision": "v2", "node": "ambient-worker", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/istio-system/istiod-8c7b98fc4-mwjfp", "name": "istiod-8c7b98fc4-mwjfp", "namespace": "istio-system", "trustDomain": "cluster.local", "serviceAccount": "isti<PERSON>", "workloadName": "isti<PERSON>", "workloadType": "deployment", "canonicalName": "isti<PERSON>", "canonicalRevision": "latest", "node": "ambient-worker2", "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "waypoint": {"destination": "/************", "port": 15008}, "protocol": "HBONE", "uid": "Kubernetes//Pod/bookinfo/reviews-v1-5b5d6494f4-qwjv4", "name": "reviews-v1-5b5d6494f4-qwjv4", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-reviews", "workloadName": "reviews-v1", "workloadType": "deployment", "canonicalName": "reviews", "canonicalRevision": "v1", "node": "ambient-worker", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "HBONE", "uid": "Kubernetes//Pod/default/productpage-v1-675fc69cf-kkrm2", "name": "productpage-v1-675fc69cf-kkrm2", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-productpage", "workloadName": "productpage-v1", "workloadType": "deployment", "canonicalName": "productpage", "canonicalRevision": "v1", "node": "ambient-worker2", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "waypoint": {"destination": "/************", "port": 15008}, "protocol": "HBONE", "uid": "Kubernetes//Pod/bookinfo/details-v1-698d88b-dqrbr", "name": "details-v1-698d88b-dqrbr", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-details", "workloadName": "details-v1", "workloadType": "deployment", "canonicalName": "details", "canonicalRevision": "v1", "node": "ambient-worker2", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/default/httpbin-7447985f87-t8hv7", "name": "httpbin-7447985f87-t8hv7", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "httpbin", "workloadName": "httpbin", "workloadType": "deployment", "canonicalName": "httpbin", "canonicalRevision": "v1", "node": "ambient-worker", "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "HBONE", "uid": "Kubernetes//Pod/default/reviews-v1-5b5d6494f4-c7z5w", "name": "reviews-v1-5b5d6494f4-c7z5w", "namespace": "default", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-reviews", "workloadName": "reviews-v1", "workloadType": "deployment", "canonicalName": "reviews", "canonicalRevision": "v1", "node": "ambient-worker", "nativeTunnel": true, "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/bookinfo/namespace-istio-waypoint-d94944bf6-z89g2", "name": "namespace-istio-waypoint-d94944bf6-z89g2", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "namespace-istio-waypoint", "workloadName": "namespace-istio-waypoint", "workloadType": "deployment", "canonicalName": "namespace-istio-waypoint", "canonicalRevision": "latest", "node": "ambient-worker2", "status": "Healthy", "clusterId": "Kubernetes"}, "/***********": {"workloadIps": ["***********"], "protocol": "TCP", "uid": "Kubernetes//Pod/bookinfo/bookinfo-productpage-istio-waypoint-5cdd6745d5-rc2gg", "name": "bookinfo-productpage-istio-waypoint-5cdd6745d5-rc2gg", "namespace": "bookinfo", "trustDomain": "cluster.local", "serviceAccount": "bookinfo-productpage-istio-waypoint", "workloadName": "bookinfo-productpage-istio-waypoint", "workloadType": "deployment", "canonicalName": "bookinfo-productpage-istio-waypoint", "canonicalRevision": "latest", "node": "ambient-worker2", "status": "Healthy", "clusterId": "Kubernetes"}}, "services": {"/************": {"name": "namespace-istio-waypoint", "namespace": "bookinfo", "hostname": "namespace-istio-waypoint.bookinfo.svc.cluster.local", "vips": ["/************"], "ports": {"15008": 15008, "15021": 15021}, "endpoints": {"Kubernetes//Pod/bookinfo/namespace-istio-waypoint-d94944bf6-z89g2:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/namespace-istio-waypoint-d94944bf6-z89g2", "service": "bookinfo/namespace-istio-waypoint.bookinfo.svc.cluster.local", "address": "/***********", "port": {"15008": 15008, "15021": 15021}}}, "subjectAltNames": []}, "/************": {"name": "gateway-api-admission-server", "namespace": "gateway-system", "hostname": "gateway-api-admission-server.gateway-system.svc.cluster.local", "vips": ["/************"], "ports": {"443": 8443}, "endpoints": {"Kubernetes//Pod/gateway-system/gateway-api-admission-server-85985d48ff-5jcvd:/**********": {"workloadUid": "Kubernetes//Pod/gateway-system/gateway-api-admission-server-85985d48ff-5jcvd", "service": "gateway-system/gateway-api-admission-server.gateway-system.svc.cluster.local", "address": "/**********", "port": {"443": 8443}}}, "subjectAltNames": []}, "/*************": {"name": "productpage", "namespace": "default", "hostname": "productpage.default.svc.cluster.local", "vips": ["/*************"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/default/productpage-v1-675fc69cf-kkrm2:/***********": {"workloadUid": "Kubernetes//Pod/default/productpage-v1-675fc69cf-kkrm2", "service": "default/productpage.default.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}, "/*************": {"name": "details", "namespace": "default", "hostname": "details.default.svc.cluster.local", "vips": ["/*************"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/default/details-v1-698d88b-krdw7:/***********": {"workloadUid": "Kubernetes//Pod/default/details-v1-698d88b-krdw7", "service": "default/details.default.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}, "/*************": {"name": "details", "namespace": "bookinfo", "hostname": "details.bookinfo.svc.cluster.local", "vips": ["/*************"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/bookinfo/details-v1-698d88b-dqrbr:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/details-v1-698d88b-dqrbr", "service": "bookinfo/details.bookinfo.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}, "/*************": {"name": "istiod-test", "namespace": "istio-system", "hostname": "istiod-test.istio-system.svc.cluster.local", "vips": ["/*************"], "ports": {"443": 15017, "15010": 15010, "15012": 15012, "15014": 15014}, "endpoints": {"Kubernetes//Pod/istio-system/istiod-test-6bdfb786d-s58pj:/***********": {"workloadUid": "Kubernetes//Pod/istio-system/istiod-test-6bdfb786d-s58pj", "service": "istio-system/istiod-test.istio-system.svc.cluster.local", "address": "/***********", "port": {"443": 15017, "15010": 15010, "15012": 15012, "15014": 15014}}}, "subjectAltNames": []}, "/***********": {"name": "ratings", "namespace": "default", "hostname": "ratings.default.svc.cluster.local", "vips": ["/***********"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/default/ratings-v1-6484c4d9bb-8xc2r:/***********": {"workloadUid": "Kubernetes//Pod/default/ratings-v1-6484c4d9bb-8xc2r", "service": "default/ratings.default.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}, "/************": {"name": "sleep", "namespace": "sleep", "hostname": "sleep.sleep.svc.cluster.local", "vips": ["/************"], "ports": {"80": 80}, "endpoints": {"Kubernetes//Pod/sleep/sleep-7656cf8794-qpvbm:/***********": {"workloadUid": "Kubernetes//Pod/sleep/sleep-7656cf8794-qpvbm", "service": "sleep/sleep.sleep.svc.cluster.local", "address": "/***********", "port": {"80": 80}}}, "subjectAltNames": []}, "/************": {"name": "ratings", "namespace": "bookinfo", "hostname": "ratings.bookinfo.svc.cluster.local", "vips": ["/************"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/bookinfo/ratings-v1-6484c4d9bb-mdxm5:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/ratings-v1-6484c4d9bb-mdxm5", "service": "bookinfo/ratings.bookinfo.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}, "/************": {"name": "reviews", "namespace": "bookinfo", "hostname": "reviews.bookinfo.svc.cluster.local", "vips": ["/************"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/bookinfo/reviews-v2-5b667bcbf8-q5pn2:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/reviews-v2-5b667bcbf8-q5pn2", "service": "bookinfo/reviews.bookinfo.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}, "Kubernetes//Pod/bookinfo/reviews-v1-5b5d6494f4-qwjv4:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/reviews-v1-5b5d6494f4-qwjv4", "service": "bookinfo/reviews.bookinfo.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}, "Kubernetes//Pod/bookinfo/reviews-v3-5b9bd44f4-7fff4:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/reviews-v3-5b9bd44f4-7fff4", "service": "bookinfo/reviews.bookinfo.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}, "/*********": {"name": "kubernetes", "namespace": "default", "hostname": "kubernetes.default.svc.cluster.local", "vips": ["/*********"], "ports": {"443": 6443}, "endpoints": {}, "subjectAltNames": []}, "/***********": {"name": "bookinfo-productpage-istio-waypoint", "namespace": "bookinfo", "hostname": "bookinfo-productpage-istio-waypoint.bookinfo.svc.cluster.local", "vips": ["/***********"], "ports": {"15008": 15008, "15021": 15021}, "endpoints": {"Kubernetes//Pod/bookinfo/bookinfo-productpage-istio-waypoint-5cdd6745d5-rc2gg:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/bookinfo-productpage-istio-waypoint-5cdd6745d5-rc2gg", "service": "bookinfo/bookinfo-productpage-istio-waypoint.bookinfo.svc.cluster.local", "address": "/***********", "port": {"15008": 15008, "15021": 15021}}}, "subjectAltNames": []}, "/*********0": {"name": "kube-dns", "namespace": "kube-system", "hostname": "kube-dns.kube-system.svc.cluster.local", "vips": ["/*********0"], "ports": {"53": 53, "9153": 9153}, "endpoints": {"Kubernetes//Pod/kube-system/coredns-5dd5756b68-mgjn9:/**********": {"workloadUid": "Kubernetes//Pod/kube-system/coredns-5dd5756b68-mgjn9", "service": "kube-system/kube-dns.kube-system.svc.cluster.local", "address": "/**********", "port": {"53": 53, "9153": 9153}}, "Kubernetes//Pod/kube-system/coredns-5dd5756b68-nzlpw:/**********": {"workloadUid": "Kubernetes//Pod/kube-system/coredns-5dd5756b68-nzlpw", "service": "kube-system/kube-dns.kube-system.svc.cluster.local", "address": "/**********", "port": {"53": 53, "9153": 9153}}}, "subjectAltNames": []}, "/************": {"name": "httpbin", "namespace": "default", "hostname": "httpbin.default.svc.cluster.local", "vips": ["/************"], "ports": {"8000": 80}, "endpoints": {"Kubernetes//Pod/default/httpbin-7447985f87-t8hv7:/***********": {"workloadUid": "Kubernetes//Pod/default/httpbin-7447985f87-t8hv7", "service": "default/httpbin.default.svc.cluster.local", "address": "/***********", "port": {"8000": 80}}}, "subjectAltNames": []}, "/*************": {"name": "httpbin", "namespace": "httpbin", "hostname": "httpbin.httpbin.svc.cluster.local", "vips": ["/*************"], "ports": {"8000": 80}, "endpoints": {"Kubernetes//Pod/httpbin/httpbin-65975d4c6f-jr69n:/***********": {"workloadUid": "Kubernetes//Pod/httpbin/httpbin-65975d4c6f-jr69n", "service": "httpbin/httpbin.httpbin.svc.cluster.local", "address": "/***********", "port": {"8000": 80}}}, "subjectAltNames": []}, "/*************": {"name": "productpage", "namespace": "bookinfo", "hostname": "productpage.bookinfo.svc.cluster.local", "vips": ["/*************"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/bookinfo/productpage-v1-675fc69cf-jscn2:/***********": {"workloadUid": "Kubernetes//Pod/bookinfo/productpage-v1-675fc69cf-jscn2", "service": "bookinfo/productpage.bookinfo.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}, "/************": {"name": "isti<PERSON>", "namespace": "istio-system", "hostname": "istiod.istio-system.svc.cluster.local", "vips": ["/************"], "ports": {"443": 15017, "15010": 15010, "15012": 15012, "15014": 15014}, "endpoints": {"Kubernetes//Pod/istio-system/istiod-8c7b98fc4-mwjfp:/***********": {"workloadUid": "Kubernetes//Pod/istio-system/istiod-8c7b98fc4-mwjfp", "service": "istio-system/istiod.istio-system.svc.cluster.local", "address": "/***********", "port": {"443": 15017, "15010": 15010, "15012": 15012, "15014": 15014}}}, "subjectAltNames": []}, "/***********": {"name": "sleep", "namespace": "default", "hostname": "sleep.default.svc.cluster.local", "vips": ["/***********"], "ports": {"80": 80}, "endpoints": {"Kubernetes//Pod/default/sleep-7656cf8794-lxcmx:/***********": {"workloadUid": "Kubernetes//Pod/default/sleep-7656cf8794-lxcmx", "service": "default/sleep.default.svc.cluster.local", "address": "/***********", "port": {"80": 80}}}, "subjectAltNames": []}, "/**********": {"name": "reviews", "namespace": "default", "hostname": "reviews.default.svc.cluster.local", "vips": ["/**********"], "ports": {"9080": 9080}, "endpoints": {"Kubernetes//Pod/default/reviews-v3-5b9bd44f4-z9ms4:/***********": {"workloadUid": "Kubernetes//Pod/default/reviews-v3-5b9bd44f4-z9ms4", "service": "default/reviews.default.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}, "Kubernetes//Pod/default/reviews-v2-5b667bcbf8-twvx6:/***********": {"workloadUid": "Kubernetes//Pod/default/reviews-v2-5b667bcbf8-twvx6", "service": "default/reviews.default.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}, "Kubernetes//Pod/default/reviews-v1-5b5d6494f4-c7z5w:/***********": {"workloadUid": "Kubernetes//Pod/default/reviews-v1-5b5d6494f4-c7z5w", "service": "default/reviews.default.svc.cluster.local", "address": "/***********", "port": {"9080": 9080}}}, "subjectAltNames": []}}, "certificates": [{"identity": "spiffe://cluster.local/ns/istio-system/sa/istiod", "state": "Available", "certChain": [{"pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIICejCCAWKgAwIBAgIRAOXftZFQsrp/EI2T3OxaphMwDQYJKoZIhvcNAQELBQAw\nGDEWMBQGA1UEChMNY2x1c3Rlci5sb2NhbDAeFw0yMzAzMjExMzAyNTdaFw0yMzAz\nMjIxMzA0NTdaMAAwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAATYcNh2oplaXXcU\n775emfKiA5Ki9wyLTn/6ksy+PXvLvIcDwnf0XX/3bJ4CBxmFO8vBpFEhnklQUNzQ\nhrU5sj+8o4GhMIGeMA4GA1UdDwEB/wQEAwIFoDAdBgNVHSUEFjAUBggrBgEFBQcD\nAQYIKwYBBQUHAwIwDAYDVR0TAQH/BAIwADAfBgNVHSMEGDAWgBTHJv/TQ6E5U30B\nesYpAA6B2CXKcTA+BgNVHREBAf8ENDAyhjBzcGlmZmU6Ly9jbHVzdGVyLmxvY2Fs\nL25zL2lzdGlvLXN5c3RlbS9zYS9pc3Rpb2QwDQYJKoZIhvcNAQELBQADggEBAFmw\nUn8mk9cNs+qxMHZN6qdljeGAcm4kp0vdY7rFGeAvtSc5lOSwFtNZC9K8bDayJAVo\n9DFy1iVH41052lcJ6VOZHQkbpVHHD38Kxi6wb1Fn7JpOhf/AR31qURIGzPHl6YFS\nwpd29DlZ8pyaHNR+zj5LcKmsFiKV5bRBbYn+sRoqtput+0tFaK+0EMkRsQk8GkEP\nVI5xMlIS5HRJwZGxqqYOh/aDm1ldKUazL0ipGg+3YTzP2fYDgp0bCgtFxiUdQJca\nRvTRMp33vNQygdouKAzlmT1p8yAV9/LSL12dF1Ac/rot6kh/Ieo+5Wlg4ydijCTF\nYWFM1Eyc7UwLHP2Gkfk=\n-----END CERTIFICATE-----\n", "serialNumber": "305554775863395697262503895661564044819", "validFrom": "2023-03-21T13:02:57Z", "expirationTime": "2033-03-22T13:04:57Z"}, {"pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIC/TCCAeWgAwIBAgIRAIpRZkXEDOdsLA0nq04kYcEwDQYJKoZIhvcNAQELBQAw\nGDEWMBQGA1UEChMNY2x1c3Rlci5sb2NhbDAeFw0yMzAzMjExMzA0NDlaFw0zMzAz\nMTgxMzA0NDlaMBgxFjAUBgNVBAoTDWNsdXN0ZXIubG9jYWwwggEiMA0GCSqGSIb3\nDQEBAQUAA4IBDwAwggEKAoIBAQC+DjsoK3KwPpPalDMYf5RlIwMoeX2nhWdqYLWW\nrRfXO9pyEySputmqoD8lqnqssJYvi3nerqJdXRjtZnJ6zYZ6Fc+QxldSpaHm4t56\nYxz0K22USMkSfEgu6PP/a5RZpPH9/LpJaSRPICXOP2GkAfF4djSUAFmRkC9XYgjX\nmk8ls01qKencIruEfSy3cTof8Uds2kyCiMOTqOvhyNKjAU3xeYJU8ztMA8k2AS0Z\nDSaf+qei6iVPTQkhqX8msBMaS1Y9kC8mYL5RchiYoknjL1xx3IxrmaIsD8qU1+In\n3GTvCD5w7+MgDKl58dHInPcFyGQSEo0nTokcWZ0p6KFvej3zAgMBAAGjQjBAMA4G\nA1UdDwEB/wQEAwICBDAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBTHJv/TQ6E5\nU30BesYpAA6B2CXKcTANBgkqhkiG9w0BAQsFAAOCAQEAh0tDT/aF0Z0GrGzshJUQ\nxtplY24pKnjcaZVyn9xfQP3S/907eNov+l4QiCV7J9ruH2ns9MJhxr88ilVfNKcl\nvPiAHBx+OnxnMYOupQQbEZ+F7uepfoRFKkQCOuh9YKoUNqd59RMl2N9mwIy1T18f\nh/7Ys6vTemPywiUYQ+USNKUfGS7Vuyi0br1XN0LzTmzk9aMaKQkpNnt5GYKwIDoC\nkxS1z5RNf9yOQVZuC1l3CajWvJS9XEm6N85tkFnSSkx+vJGljbR1NcIkhZfP9tA6\nqVGI7QfvPgIjeNsQWhlBqGRozHujMo1ekJVNN+cdbF7fUG2mIsrCx/707ZVKXSMj\nqg==\n-----END CERTIFICATE-----\n", "serialNumber": "183856113797057159350158029893417591233", "validFrom": "2022-03-21T13:04:49Z", "expirationTime": "2022-03-18T13:04:49Z"}]}, {"identity": "spiffe://cluster.local/ns/istio-system/sa/ztunnel", "state": "Initializing", "certChain": []}, {"identity": "spiffe://cluster.local/ns/istio-system/sa/another-sa", "state": "Unavailable: the identity is no longer needed", "certChain": []}, {"identity": "spiffe://cluster.local/ns/istio-system/sa/istiod", "state": "Unavailable: signing gRPC error (The service is currently unavailable): error trying to connect: TLS handshake failed: cert verification failed - unable to get local issuer certificate [CERTIFICATE_VERIFY_FAILED]", "certChain": []}]}