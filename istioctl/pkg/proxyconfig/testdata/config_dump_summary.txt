Istio Version:       1.21.0-1226
Istio Proxy Version: ee85c5f28702f00621aed895915fca565796b9e4
Envoy Version:       0.0.0

NAME                                                                SERVICE FQDN                          PORT     SUBSET     DIRECTION       TYPE       DESTINATION RULE
cluster/agent                                                       cluster/agent                         -        -          -               STATIC     
cluster/inbound-vip|8000|http|httpbin.default.svc.cluster.local     httpbin.default.svc.cluster.local     8000     http       inbound-vip     EDS        
cluster/prometheus_stats                                            cluster/prometheus_stats              -        -          -               STATIC     

NAME                   ADDRESSES PORT  MATCH DESTINATION
listener/main_internal           0     ALL   Cluster: inbound-vip|8100|http|httpbin.default.svc.cluster.local
listener/main_internal           0     ALL   Cluster: inbound-vip|8000|http|httpbin.default.svc.cluster.local
listener/main_internal           0     ALL   Cluster: encap
listener/main_internal           0     ALL   Cluster: encap
listener/              0.0.0.0   15021 ALL   Inline Route: /healthz/ready*

NAME                                                              VHOST NAME            DOMAINS     MATCH     VIRTUAL SERVICE
route/inbound-vip|8000|http|httpbin.default.svc.cluster.local     inbound|http|8000     *           /*        

RESOURCE NAME      TYPE           STATUS     VALID CERT     SERIAL NUMBER                        NOT AFTER                NOT BEFORE
secret/default     Cert Chain     ACTIVE     false          6fbee254c22900615cb1f74e3d2f1713     2023-05-16T01:32:52Z     2023-05-15T01:30:52Z
secret/ROOTCA      CA             ACTIVE     true           193a543fe2b0d9cd4847675394dfc54      2033-05-02T03:41:33Z     2023-05-05T03:41:33Z

NAME                                                       STATUS      LOCALITY     CLUSTER
endpoint/envoy://connect_originate/***************:800     HEALTHY                  inbound-vip|8100|http|httpbin.default.svc.cluster.local
endpoint/127.0.0.1:15000                                   HEALTHY                  prometheus_stats
