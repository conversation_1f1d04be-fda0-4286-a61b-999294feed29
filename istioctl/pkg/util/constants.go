// Copyright Istio Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package util

const (
	// DefaultProxyAdminPort is the default port for the proxy admin server
	DefaultProxyAdminPort = 15000

	// DefaultMeshConfigMapName is the default name of the ConfigMap with the mesh config
	// The actual name can be different - use getMeshConfigMapName
	DefaultMeshConfigMapName = "istio"

	// ConfigMapKey should match the expected MeshConfig file name
	ConfigMapKey = "mesh"

	// ValuesConfigMapKey should match the expected Values file name
	ValuesConfigMapKey = "values"

	DefaultRevisionName = "default"
)

const (
	// ExperimentalMsg indicate active development and not for production use warning.
	ExperimentalMsg = `THIS COMMAND IS UNDER ACTIVE DEVELOPMENT AND NOT READY FOR PRODUCTION USE.`
)

const (
	JSONFormat  = "json"
	YamlFormat  = "yaml"
	TableFormat = "table"
)
